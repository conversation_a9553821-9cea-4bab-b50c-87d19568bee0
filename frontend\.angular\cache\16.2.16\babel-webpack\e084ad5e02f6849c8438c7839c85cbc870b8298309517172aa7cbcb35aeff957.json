{"ast": null, "code": "export var MessageType;\n(function (MessageType) {\n  MessageType[\"TEXT\"] = \"TEXT\";\n  MessageType[\"IMAGE\"] = \"IMAGE\";\n  MessageType[\"FILE\"] = \"FILE\";\n  MessageType[\"AUDIO\"] = \"AUDIO\";\n  MessageType[\"VIDEO\"] = \"VIDEO\";\n  MessageType[\"SYSTEM\"] = \"SYSTEM\";\n  MessageType[\"VOICE_MESSAGE\"] = \"VOICE_MESSAGE\";\n  MessageType[\"TEXT_LOWER\"] = \"text\";\n  MessageType[\"IMAGE_LOWER\"] = \"image\";\n  MessageType[\"FILE_LOWER\"] = \"file\";\n  MessageType[\"AUDIO_LOWER\"] = \"audio\";\n  MessageType[\"VIDEO_LOWER\"] = \"video\";\n  MessageType[\"SYSTEM_LOWER\"] = \"system\";\n  MessageType[\"VOICE_MESSAGE_LOWER\"] = \"voice_message\";\n})(MessageType || (MessageType = {}));\nexport var MessageStatus;\n(function (MessageStatus) {\n  MessageStatus[\"SENDING\"] = \"SENDING\";\n  MessageStatus[\"SENT\"] = \"SENT\";\n  MessageStatus[\"DELIVERED\"] = \"DELIVERED\";\n  MessageStatus[\"READ\"] = \"READ\";\n  MessageStatus[\"FAILED\"] = \"FAILED\";\n})(MessageStatus || (MessageStatus = {}));\n// --------------------------------------------------------------------------\n// Types et interfaces pour les appels\n// --------------------------------------------------------------------------\n/**\n * Types d'appels possibles\n */\nexport var CallType;\n(function (CallType) {\n  CallType[\"AUDIO\"] = \"AUDIO\";\n  CallType[\"VIDEO\"] = \"VIDEO\";\n  CallType[\"VIDEO_ONLY\"] = \"VIDEO_ONLY\";\n})(CallType || (CallType = {}));\n/**\n * États possibles d'un appel\n */\nexport var CallStatus;\n(function (CallStatus) {\n  CallStatus[\"RINGING\"] = \"RINGING\";\n  CallStatus[\"CONNECTED\"] = \"CONNECTED\";\n  CallStatus[\"ENDED\"] = \"ENDED\";\n  CallStatus[\"MISSED\"] = \"MISSED\";\n  CallStatus[\"REJECTED\"] = \"REJECTED\";\n  CallStatus[\"FAILED\"] = \"FAILED\";\n})(CallStatus || (CallStatus = {}));", "map": {"version": 3, "names": ["MessageType", "MessageStatus", "CallType", "CallStatus"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\models\\message.model.ts"], "sourcesContent": ["export interface User {\r\n  // <PERSON><PERSON> principaux (premier système)\r\n  _id: string;\r\n  id?: string;\r\n  username: string;\r\n  email: string;\r\n  fullName?: string;\r\n  profileImage?: string;\r\n  image?: string | null;\r\n  role: string;\r\n  bio?: string;\r\n  isActive: boolean;\r\n  isOnline?: boolean;\r\n  lastActive?: Date;\r\n  createdAt?: Date;\r\n  updatedAt?: Date;\r\n  followingCount?: number;\r\n  followersCount?: number;\r\n  postCount?: number;\r\n  group?: any;\r\n  verified?: boolean;\r\n  __v?: number;\r\n\r\n  // Profile completion tracking\r\n  isFirstLogin?: boolean;\r\n  profileCompletionPercentage?: number;\r\n  isProfileComplete?: boolean;\r\n\r\n  // Nouveaux champs (second système)\r\n  firstName?: string; // Prénom\r\n  lastName?: string; // Nom\r\n  profession?: string; // Étudiant ou Professeur\r\n  dateOfBirth?: Date | string; // Date de naissance\r\n\r\n  // Champs supplémentaires pour la compatibilité\r\n  name?: string;\r\n  password?: string;\r\n  department?: string;\r\n  position?: string;\r\n  phoneNumber?: string;\r\n  address?: string;\r\n  profilePicture?: string;\r\n  skills?: string[];\r\n  joinDate?: Date;\r\n}\r\nexport interface PaginatedResponse<T> {\r\n  items: T[];\r\n  totalCount: number;\r\n  pageInfo: {\r\n    currentPage: number;\r\n    perPage: number;\r\n    hasNextPage: boolean;\r\n  };\r\n}\r\nexport interface Toast {\r\n  id?: number;\r\n  type: 'success' | 'error' | 'warning' | 'info';\r\n  message: string;\r\n  duration?: number;\r\n}\r\nexport enum MessageType {\r\n  TEXT = 'TEXT',\r\n  IMAGE = 'IMAGE',\r\n  FILE = 'FILE',\r\n  AUDIO = 'AUDIO',\r\n  VIDEO = 'VIDEO',\r\n  SYSTEM = 'SYSTEM',\r\n  VOICE_MESSAGE = 'VOICE_MESSAGE',\r\n  TEXT_LOWER = 'text',\r\n  IMAGE_LOWER = 'image',\r\n  FILE_LOWER = 'file',\r\n  AUDIO_LOWER = 'audio',\r\n  VIDEO_LOWER = 'video',\r\n  SYSTEM_LOWER = 'system',\r\n  VOICE_MESSAGE_LOWER = 'voice_message',\r\n}\r\nexport enum MessageStatus {\r\n  SENDING = 'SENDING',\r\n  SENT = 'SENT',\r\n  DELIVERED = 'DELIVERED',\r\n  READ = 'READ',\r\n  FAILED = 'FAILED',\r\n}\r\nexport interface Attachment {\r\n  url: string;\r\n  id?: string;\r\n  _id?: string; // Pour compatibilité avec MongoDB\r\n  type: MessageType;\r\n  name?: string;\r\n  size?: number;\r\n  mimeType?: string;\r\n  thumbnailUrl?: string;\r\n  duration?: number;\r\n}\r\nexport interface Reaction {\r\n  userId: string;\r\n  user: Partial<User>;\r\n  emoji: string;\r\n  createdAt: Date | string;\r\n}\r\nexport interface Message {\r\n  id?: string;\r\n  _id?: string; // Pour compatibilité avec MongoDB\r\n  content?: string;\r\n  type?: MessageType;\r\n  timestamp?: Date | string;\r\n  isRead?: boolean;\r\n  readAt?: Date | string;\r\n  sender?: Partial<User>;\r\n  senderId?: string; // Pour compatibilité avec MongoDB\r\n  receiver?: Partial<User>;\r\n  receiverId?: string; // Pour compatibilité avec MongoDB\r\n  group?: Partial<Group>;\r\n  conversationId?: string;\r\n  attachments?: Attachment[];\r\n  status?: MessageStatus;\r\n  isEdited?: boolean;\r\n  isDeleted?: boolean;\r\n  deletedAt?: Date | string;\r\n  pinned?: boolean;\r\n  pinnedAt?: Date | string;\r\n  pinnedBy?: Partial<User>;\r\n  forwardedFrom?: Partial<Message>;\r\n  replyTo?: Partial<Message>;\r\n  reactions?: Reaction[];\r\n  metadata?: any;\r\n  // Propriétés pour l'état d'envoi du message\r\n  isPending?: boolean;\r\n  isError?: boolean;\r\n  isDelivered?: boolean; // Indique si le message a été livré\r\n}\r\nexport interface MessageFilter {\r\n  isRead?: boolean;\r\n  isDeleted?: boolean;\r\n  type?: MessageType;\r\n  senderId?: string;\r\n  receiverId?: string;\r\n  groupId?: string;\r\n  conversationId?: string;\r\n  pinned?: boolean;\r\n  dateFrom?: Date | string;\r\n  dateTo?: Date | string;\r\n}\r\nexport interface Conversation {\r\n  id?: string;\r\n  _id?: string; // Pour compatibilité avec MongoDB\r\n  participants?: User[];\r\n  messages?: Message[];\r\n  lastMessage?: Message | null;\r\n  lastMessageId?: string;\r\n  unreadCount?: number;\r\n  messageCount?: number;\r\n  isGroup?: boolean;\r\n  groupName?: string;\r\n  groupPhoto?: string;\r\n  groupDescription?: string;\r\n  groupAdmins?: User[];\r\n  pinnedMessages?: Message[];\r\n  typingUsers?: User[];\r\n  lastRead?: UserReadStatus[];\r\n  createdAt?: Date | string;\r\n  updatedAt?: Date | string;\r\n}\r\nexport interface UserReadStatus {\r\n  userId: string;\r\n  user: User;\r\n  readAt: Date | string;\r\n}\r\nexport interface Group {\r\n  id: string;\r\n  name: string;\r\n  photo?: string;\r\n  description?: string;\r\n  participants: User[];\r\n  admins: User[];\r\n  messageCount: number;\r\n  createdAt?: Date | string;\r\n  updatedAt?: Date | string;\r\n}\r\nexport interface GetConversationsResponse {\r\n  getConversations: Conversation[];\r\n}\r\nexport interface GetConversationResponse {\r\n  getConversation: Conversation;\r\n}\r\nexport interface GetGroupResponse {\r\n  getGroup: Group;\r\n}\r\nexport interface GetUserGroupsResponse {\r\n  getUserGroups: Group[];\r\n}\r\nexport interface UserPaginatedResponse {\r\n  users: User[];\r\n  totalCount: number;\r\n  totalPages: number;\r\n  currentPage: number;\r\n  hasNextPage: boolean;\r\n  hasPreviousPage: boolean;\r\n}\r\n\r\nexport interface GetAllUsersResponse {\r\n  getAllUsers: UserPaginatedResponse;\r\n}\r\nexport interface GetOneUserResponse {\r\n  getOneUser: User;\r\n}\r\nexport interface getCurrentUserResponse {\r\n  getCurrentUser: User;\r\n}\r\nexport interface SearchMessagesResponse {\r\n  searchMessages: Message[];\r\n}\r\nexport interface GetUnreadMessagesResponse {\r\n  getUnreadMessages: Message[];\r\n}\r\nexport interface TypingIndicatorEvents {\r\n  typingIndicator: TypingIndicatorEvent;\r\n}\r\nexport interface TypingIndicatorEvent {\r\n  conversationId: string;\r\n  userId: string;\r\n  isTyping: boolean;\r\n}\r\nexport interface MessageDeleteResponse {\r\n  deleteMessage: {\r\n    id: string;\r\n    isDeleted: boolean;\r\n    deletedAt?: string | Date;\r\n  };\r\n}\r\nexport interface MessageEditResponse {\r\n  editMessage: {\r\n    id: string;\r\n    content: string;\r\n    isEdited: boolean;\r\n    updatedAt: string | Date;\r\n  };\r\n}\r\nexport interface MessageReadEvent {\r\n  messageId: string;\r\n  readerId: string;\r\n  readAt: Date | string;\r\n}\r\nexport interface MessageReactionEvent {\r\n  messageId: string;\r\n  reactions: Reaction[];\r\n}\r\nexport interface MessageSentEvent {\r\n  messageSent: Message;\r\n}\r\nexport interface UserStatusChangedEvent {\r\n  userStatusChanged: User;\r\n}\r\nexport interface ConversationUpdatedEvent {\r\n  conversationUpdated: Conversation;\r\n}\r\nexport interface SendMessageResponse {\r\n  sendMessage: Message;\r\n}\r\nexport interface MarkAsReadResponse {\r\n  markMessageAsRead: Message;\r\n}\r\nexport interface ReactToMessageResponse {\r\n  reactToMessage: Message;\r\n}\r\nexport interface ForwardMessageResponse {\r\n  forwardMessage: Message[];\r\n}\r\nexport interface PinMessageResponse {\r\n  pinMessage: Message;\r\n}\r\nexport interface CreateGroupResponse {\r\n  createGroup: Group;\r\n}\r\nexport interface UpdateGroupResponse {\r\n  updateGroup: Group;\r\n}\r\nexport interface SetUserOnlineResponse {\r\n  setUserOnline: User;\r\n}\r\nexport interface SetUserOfflineResponse {\r\n  setUserOffline: User;\r\n}\r\nexport interface StopTypingResponse {\r\n  stopTyping: boolean;\r\n}\r\nexport interface StartTupingResponse {\r\n  startTyping: boolean;\r\n}\r\nexport interface conversationUpdatedResponse {\r\n  conversationUpdated: Conversation;\r\n}\r\nexport interface getNotificationAttachmentsEvent {\r\n  getNotificationAttachments: Attachment[];\r\n}\r\n\r\nexport interface NotificationAttachment {\r\n  url: string;\r\n  type: AttachmentType;\r\n  name?: string;\r\n  size?: number;\r\n  mimeType?: string;\r\n}\r\nexport interface NotificationMessage {\r\n  id?: string;\r\n  content: string;\r\n  attachments?: NotificationAttachment[];\r\n}\r\nexport interface NotificationSender {\r\n  id: string;\r\n  username: string;\r\n  image?: string | null;\r\n}\r\nexport interface Notification {\r\n  id: string;\r\n  type: NotificationType;\r\n  content: string;\r\n  timestamp: Date | string;\r\n  isRead: boolean;\r\n  senderId?: NotificationSender;\r\n  message?: NotificationMessage;\r\n  readAt?: Date | string;\r\n  relatedEntity?: string;\r\n  metadata?: Record<string, any>;\r\n  conversationId?: string;\r\n  groupId?: string;\r\n  isDeleting?: boolean; // Indique si la notification est en cours de suppression\r\n}\r\nexport interface GetNotificationsResponse {\r\n  getUserNotifications: Notification[];\r\n}\r\nexport interface NotificationReceivedEvent {\r\n  notificationReceived: Notification;\r\n}\r\nexport interface NotificationsReadEvent {\r\n  notificationsRead: string[];\r\n}\r\nexport interface MarkNotificationsAsReadResponse {\r\n  markNotificationsAsRead: {\r\n    success: boolean;\r\n    readCount: number;\r\n    remainingCount: number;\r\n  };\r\n}\r\nexport interface getUserNotificationsResponse {\r\n  getUserNotifications: Notification[];\r\n}\r\nexport type NotificationType =\r\n  | 'NEW_MESSAGE'\r\n  | 'FRIEND_REQUEST'\r\n  | 'GROUP_INVITE'\r\n  | 'MESSAGE_REACTION'\r\n  | 'SYSTEM_ALERT';\r\n\r\nexport type AttachmentType =\r\n  | 'IMAGE'\r\n  | 'FILE'\r\n  | 'AUDIO'\r\n  | 'VIDEO'\r\n  | 'OTHER'\r\n  | 'image'\r\n  | 'file'\r\n  | 'audio'\r\n  | 'video'\r\n  | 'other';\r\n\r\n// --------------------------------------------------------------------------\r\n// Types et interfaces pour les appels\r\n// --------------------------------------------------------------------------\r\n\r\n/**\r\n * Types d'appels possibles\r\n */\r\nexport enum CallType {\r\n  AUDIO = 'AUDIO',\r\n  VIDEO = 'VIDEO',\r\n  VIDEO_ONLY = 'VIDEO_ONLY',\r\n}\r\n\r\n/**\r\n * États possibles d'un appel\r\n */\r\nexport enum CallStatus {\r\n  RINGING = 'RINGING',\r\n  CONNECTED = 'CONNECTED',\r\n  ENDED = 'ENDED',\r\n  MISSED = 'MISSED',\r\n  REJECTED = 'REJECTED',\r\n  FAILED = 'FAILED',\r\n}\r\n\r\n/**\r\n * Interface pour un appel\r\n */\r\nexport interface Call {\r\n  id: string;\r\n  caller: User;\r\n  recipient: User;\r\n  type: CallType;\r\n  status: CallStatus;\r\n  startTime: string;\r\n  endTime?: string;\r\n  duration?: number;\r\n  conversationId?: string;\r\n  metadata?: any;\r\n}\r\n\r\n/**\r\n * Interface pour un signal d'appel\r\n */\r\nexport interface CallSignal {\r\n  callId: string;\r\n  senderId: string;\r\n  type: string;\r\n  data: string;\r\n  timestamp: string;\r\n}\r\n\r\n/**\r\n * Interface pour un appel entrant\r\n */\r\nexport interface IncomingCall {\r\n  id: string;\r\n  caller: User;\r\n  type: CallType;\r\n  conversationId?: string;\r\n  offer: string;\r\n  timestamp: string;\r\n}\r\n\r\n/**\r\n * Interface pour les options d'appel\r\n */\r\nexport interface CallOptions {\r\n  enableVideo?: boolean;\r\n  enableAudio?: boolean;\r\n  quality?: string;\r\n}\r\n\r\n/**\r\n * Interface pour les commentaires sur un appel\r\n */\r\nexport interface CallFeedback {\r\n  quality?: number;\r\n  issues?: string[];\r\n  comment?: string;\r\n}\r\n\r\n/**\r\n * Interface pour le résultat d'une opération d'appel\r\n */\r\nexport interface CallSuccess {\r\n  success: boolean;\r\n  message?: string;\r\n}\r\n"], "mappings": "AA4DA,WAAYA,WAeX;AAfD,WAAYA,WAAW;EACrBA,WAAA,iBAAa;EACbA,WAAA,mBAAe;EACfA,WAAA,iBAAa;EACbA,WAAA,mBAAe;EACfA,WAAA,mBAAe;EACfA,WAAA,qBAAiB;EACjBA,WAAA,mCAA+B;EAC/BA,WAAA,uBAAmB;EACnBA,WAAA,yBAAqB;EACrBA,WAAA,uBAAmB;EACnBA,WAAA,yBAAqB;EACrBA,WAAA,yBAAqB;EACrBA,WAAA,2BAAuB;EACvBA,WAAA,yCAAqC;AACvC,CAAC,EAfWA,WAAW,KAAXA,WAAW;AAgBvB,WAAYC,aAMX;AAND,WAAYA,aAAa;EACvBA,aAAA,uBAAmB;EACnBA,aAAA,iBAAa;EACbA,aAAA,2BAAuB;EACvBA,aAAA,iBAAa;EACbA,aAAA,qBAAiB;AACnB,CAAC,EANWA,aAAa,KAAbA,aAAa;AAkSzB;AACA;AACA;AAEA;;;AAGA,WAAYC,QAIX;AAJD,WAAYA,QAAQ;EAClBA,QAAA,mBAAe;EACfA,QAAA,mBAAe;EACfA,QAAA,6BAAyB;AAC3B,CAAC,EAJWA,QAAQ,KAARA,QAAQ;AAMpB;;;AAGA,WAAYC,UAOX;AAPD,WAAYA,UAAU;EACpBA,UAAA,uBAAmB;EACnBA,UAAA,2BAAuB;EACvBA,UAAA,mBAAe;EACfA,UAAA,qBAAiB;EACjBA,UAAA,yBAAqB;EACrBA,UAAA,qBAAiB;AACnB,CAAC,EAPWA,UAAU,KAAVA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}