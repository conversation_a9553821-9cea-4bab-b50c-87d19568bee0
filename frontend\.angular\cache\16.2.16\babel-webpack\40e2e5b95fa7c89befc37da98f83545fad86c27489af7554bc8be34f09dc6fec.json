{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { Validators } from '@angular/forms';\nexport let ProfileCompletionComponent = class ProfileCompletionComponent {\n  constructor(fb, authService, dataService, router) {\n    this.fb = fb;\n    this.authService = authService;\n    this.dataService = dataService;\n    this.router = router;\n    this.currentUser = null;\n    this.progressPercentage = 0;\n    this.isLoading = false;\n    this.message = '';\n    this.error = '';\n    this.selectedFile = null;\n    this.previewUrl = null;\n    // Form steps for better UX\n    this.currentStep = 1;\n    this.totalSteps = 3;\n    this.profileForm = this.fb.group({\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      dateOfBirth: ['', Validators.required],\n      phoneNumber: ['', [Validators.required, Validators.pattern(/^[0-9+\\-\\s()]+$/)]],\n      department: ['', Validators.required],\n      position: [''],\n      bio: ['', [Validators.required, Validators.minLength(10)]],\n      address: [''],\n      skills: ['']\n    });\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.getCurrentUser();\n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    // Check if profile is already complete\n    if (this.currentUser.isProfileComplete) {\n      this.router.navigate(['/']);\n      return;\n    }\n    this.calculateProgress();\n    this.prefillForm();\n  }\n  prefillForm() {\n    if (this.currentUser) {\n      this.profileForm.patchValue({\n        firstName: this.currentUser.firstName || '',\n        lastName: this.currentUser.lastName || '',\n        dateOfBirth: this.currentUser.dateOfBirth || '',\n        phoneNumber: this.currentUser.phoneNumber || '',\n        department: this.currentUser.department || '',\n        position: this.currentUser.position || '',\n        bio: this.currentUser.bio || '',\n        address: this.currentUser.address || '',\n        skills: this.currentUser.skills?.join(', ') || ''\n      });\n    }\n  }\n  calculateProgress() {\n    const formValues = this.profileForm.value;\n    const requiredFields = ['firstName', 'lastName', 'dateOfBirth', 'phoneNumber', 'department', 'bio'];\n    const optionalFields = ['position', 'address', 'skills'];\n    let completedRequired = 0;\n    let completedOptional = 0;\n    // Check required fields\n    requiredFields.forEach(field => {\n      if (formValues[field] && formValues[field].toString().trim() !== '') {\n        completedRequired++;\n      }\n    });\n    // Check optional fields\n    optionalFields.forEach(field => {\n      if (formValues[field] && formValues[field].toString().trim() !== '') {\n        completedOptional++;\n      }\n    });\n    // Check profile image\n    let hasProfileImage = 0;\n    if (this.selectedFile || this.currentUser?.profileImage && this.currentUser.profileImage !== 'uploads/default.png') {\n      hasProfileImage = 1;\n    }\n    // Calculate percentage: Required fields (60%) + Optional fields (30%) + Profile Image (10%)\n    const requiredPercentage = completedRequired / requiredFields.length * 60;\n    const optionalPercentage = completedOptional / optionalFields.length * 30;\n    const imagePercentage = hasProfileImage * 10;\n    this.progressPercentage = Math.round(requiredPercentage + optionalPercentage + imagePercentage);\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.selectedFile = file;\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = () => {\n        this.previewUrl = reader.result;\n        this.calculateProgress();\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  nextStep() {\n    if (this.currentStep < this.totalSteps) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  onSubmit() {\n    if (this.profileForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    this.isLoading = true;\n    this.error = '';\n    this.message = '';\n    const formData = new FormData();\n    // Add form fields\n    Object.keys(this.profileForm.value).forEach(key => {\n      const value = this.profileForm.value[key];\n      if (key === 'skills' && value) {\n        // Convert skills string to array\n        const skillsArray = value.split(',').map(skill => skill.trim()).filter(skill => skill);\n        formData.append(key, JSON.stringify(skillsArray));\n      } else if (value) {\n        formData.append(key, value);\n      }\n    });\n    // Add profile image if selected\n    if (this.selectedFile) {\n      formData.append('image', this.selectedFile);\n    }\n    this.dataService.completeProfile(formData).subscribe({\n      next: response => {\n        this.isLoading = false;\n        this.message = 'Profile completed successfully!';\n        // Update current user\n        this.authService.setCurrentUser(response.user);\n        // Redirect to home after a short delay\n        setTimeout(() => {\n          this.router.navigate(['/']);\n        }, 2000);\n      },\n      error: err => {\n        this.isLoading = false;\n        this.error = err.error?.message || 'An error occurred while completing your profile.';\n      }\n    });\n  }\n  skipForNow() {\n    // Allow user to skip but warn them\n    if (confirm('Are you sure you want to skip profile completion? You can complete it later from your profile page.')) {\n      this.router.navigate(['/']);\n    }\n  }\n  markFormGroupTouched() {\n    Object.keys(this.profileForm.controls).forEach(key => {\n      this.profileForm.get(key)?.markAsTouched();\n    });\n  }\n  // Helper methods for template\n  getFieldError(fieldName) {\n    const field = this.profileForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['minlength']) return `${fieldName} is too short`;\n      if (field.errors['pattern']) return `${fieldName} format is invalid`;\n    }\n    return '';\n  }\n  isFieldInvalid(fieldName) {\n    const field = this.profileForm.get(fieldName);\n    return !!(field?.invalid && field.touched);\n  }\n  getMotivationalMessage() {\n    if (this.progressPercentage < 25) {\n      return \"Great start! Let's build your amazing profile together! 🚀\";\n    } else if (this.progressPercentage < 50) {\n      return \"You're making excellent progress! Keep going! 💪\";\n    } else if (this.progressPercentage < 75) {\n      return \"Fantastic! You're more than halfway there! 🌟\";\n    } else if (this.progressPercentage < 100) {\n      return \"Almost done! Just a few more details to go! 🎯\";\n    } else {\n      return \"Perfect! Your profile is complete and ready to shine! ✨\";\n    }\n  }\n  getProgressColor() {\n    if (this.progressPercentage < 25) return '#ef4444'; // red\n    if (this.progressPercentage < 50) return '#f97316'; // orange\n    if (this.progressPercentage < 75) return '#eab308'; // yellow\n    if (this.progressPercentage < 100) return '#22c55e'; // green\n    return '#10b981'; // emerald\n  }\n};\n\nProfileCompletionComponent = __decorate([Component({\n  selector: 'app-profile-completion',\n  templateUrl: './profile-completion.component.html',\n  styleUrls: ['./profile-completion.component.css']\n})], ProfileCompletionComponent);", "map": {"version": 3, "names": ["Component", "Validators", "ProfileCompletionComponent", "constructor", "fb", "authService", "dataService", "router", "currentUser", "progressPercentage", "isLoading", "message", "error", "selectedFile", "previewUrl", "currentStep", "totalSteps", "profileForm", "group", "firstName", "required", "<PERSON><PERSON><PERSON><PERSON>", "lastName", "dateOfBirth", "phoneNumber", "pattern", "department", "position", "bio", "address", "skills", "ngOnInit", "getCurrentUser", "navigate", "isProfileComplete", "calculateProgress", "prefillForm", "patchValue", "join", "formValues", "value", "requiredFields", "optionalFields", "completedRequired", "completedOptional", "for<PERSON>ach", "field", "toString", "trim", "hasProfileImage", "profileImage", "requiredPercentage", "length", "optionalPercentage", "imagePercentage", "Math", "round", "onFileSelected", "event", "file", "target", "files", "reader", "FileReader", "onload", "result", "readAsDataURL", "nextStep", "previousStep", "onSubmit", "invalid", "markFormGroupTouched", "formData", "FormData", "Object", "keys", "key", "skillsArray", "split", "map", "skill", "filter", "append", "JSON", "stringify", "completeProfile", "subscribe", "next", "response", "setCurrentUser", "user", "setTimeout", "err", "skipFor<PERSON>ow", "confirm", "controls", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "getFieldError", "fieldName", "errors", "touched", "isFieldInvalid", "getMotivationalMessage", "getProgressColor", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\profile-completion\\profile-completion.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { DataService } from 'src/app/services/data.service';\nimport { User } from 'src/app/models/user.model';\n\n@Component({\n  selector: 'app-profile-completion',\n  templateUrl: './profile-completion.component.html',\n  styleUrls: ['./profile-completion.component.css']\n})\nexport class ProfileCompletionComponent implements OnInit {\n  profileForm: FormGroup;\n  currentUser: User | null = null;\n  progressPercentage: number = 0;\n  isLoading = false;\n  message = '';\n  error = '';\n  selectedFile: File | null = null;\n  previewUrl: string | ArrayBuffer | null = null;\n\n  // Form steps for better UX\n  currentStep = 1;\n  totalSteps = 3;\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthuserService,\n    private dataService: DataService,\n    private router: Router\n  ) {\n    this.profileForm = this.fb.group({\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      dateOfBirth: ['', Validators.required],\n      phoneNumber: ['', [Validators.required, Validators.pattern(/^[0-9+\\-\\s()]+$/)]],\n      department: ['', Validators.required],\n      position: [''],\n      bio: ['', [Validators.required, Validators.minLength(10)]],\n      address: [''],\n      skills: ['']\n    });\n  }\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.getCurrentUser();\n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n\n    // Check if profile is already complete\n    if (this.currentUser.isProfileComplete) {\n      this.router.navigate(['/']);\n      return;\n    }\n\n    this.calculateProgress();\n    this.prefillForm();\n  }\n\n  prefillForm(): void {\n    if (this.currentUser) {\n      this.profileForm.patchValue({\n        firstName: this.currentUser.firstName || '',\n        lastName: this.currentUser.lastName || '',\n        dateOfBirth: this.currentUser.dateOfBirth || '',\n        phoneNumber: this.currentUser.phoneNumber || '',\n        department: this.currentUser.department || '',\n        position: this.currentUser.position || '',\n        bio: this.currentUser.bio || '',\n        address: this.currentUser.address || '',\n        skills: this.currentUser.skills?.join(', ') || ''\n      });\n    }\n  }\n\n  calculateProgress(): void {\n    const formValues = this.profileForm.value;\n    const requiredFields = ['firstName', 'lastName', 'dateOfBirth', 'phoneNumber', 'department', 'bio'];\n    const optionalFields = ['position', 'address', 'skills'];\n    \n    let completedRequired = 0;\n    let completedOptional = 0;\n\n    // Check required fields\n    requiredFields.forEach(field => {\n      if (formValues[field] && formValues[field].toString().trim() !== '') {\n        completedRequired++;\n      }\n    });\n\n    // Check optional fields\n    optionalFields.forEach(field => {\n      if (formValues[field] && formValues[field].toString().trim() !== '') {\n        completedOptional++;\n      }\n    });\n\n    // Check profile image\n    let hasProfileImage = 0;\n    if (this.selectedFile || (this.currentUser?.profileImage && this.currentUser.profileImage !== 'uploads/default.png')) {\n      hasProfileImage = 1;\n    }\n\n    // Calculate percentage: Required fields (60%) + Optional fields (30%) + Profile Image (10%)\n    const requiredPercentage = (completedRequired / requiredFields.length) * 60;\n    const optionalPercentage = (completedOptional / optionalFields.length) * 30;\n    const imagePercentage = hasProfileImage * 10;\n\n    this.progressPercentage = Math.round(requiredPercentage + optionalPercentage + imagePercentage);\n  }\n\n  onFileSelected(event: any): void {\n    const file = event.target.files[0];\n    if (file) {\n      this.selectedFile = file;\n      \n      // Create preview\n      const reader = new FileReader();\n      reader.onload = () => {\n        this.previewUrl = reader.result;\n        this.calculateProgress();\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n\n  nextStep(): void {\n    if (this.currentStep < this.totalSteps) {\n      this.currentStep++;\n    }\n  }\n\n  previousStep(): void {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n\n  onSubmit(): void {\n    if (this.profileForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    this.isLoading = true;\n    this.error = '';\n    this.message = '';\n\n    const formData = new FormData();\n    \n    // Add form fields\n    Object.keys(this.profileForm.value).forEach(key => {\n      const value = this.profileForm.value[key];\n      if (key === 'skills' && value) {\n        // Convert skills string to array\n        const skillsArray = value.split(',').map((skill: string) => skill.trim()).filter((skill: string) => skill);\n        formData.append(key, JSON.stringify(skillsArray));\n      } else if (value) {\n        formData.append(key, value);\n      }\n    });\n\n    // Add profile image if selected\n    if (this.selectedFile) {\n      formData.append('image', this.selectedFile);\n    }\n\n    this.dataService.completeProfile(formData).subscribe({\n      next: (response: any) => {\n        this.isLoading = false;\n        this.message = 'Profile completed successfully!';\n        \n        // Update current user\n        this.authService.setCurrentUser(response.user);\n        \n        // Redirect to home after a short delay\n        setTimeout(() => {\n          this.router.navigate(['/']);\n        }, 2000);\n      },\n      error: (err) => {\n        this.isLoading = false;\n        this.error = err.error?.message || 'An error occurred while completing your profile.';\n      }\n    });\n  }\n\n  skipForNow(): void {\n    // Allow user to skip but warn them\n    if (confirm('Are you sure you want to skip profile completion? You can complete it later from your profile page.')) {\n      this.router.navigate(['/']);\n    }\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.profileForm.controls).forEach(key => {\n      this.profileForm.get(key)?.markAsTouched();\n    });\n  }\n\n  // Helper methods for template\n  getFieldError(fieldName: string): string {\n    const field = this.profileForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['minlength']) return `${fieldName} is too short`;\n      if (field.errors['pattern']) return `${fieldName} format is invalid`;\n    }\n    return '';\n  }\n\n  isFieldInvalid(fieldName: string): boolean {\n    const field = this.profileForm.get(fieldName);\n    return !!(field?.invalid && field.touched);\n  }\n\n  getMotivationalMessage(): string {\n    if (this.progressPercentage < 25) {\n      return \"Great start! Let's build your amazing profile together! 🚀\";\n    } else if (this.progressPercentage < 50) {\n      return \"You're making excellent progress! Keep going! 💪\";\n    } else if (this.progressPercentage < 75) {\n      return \"Fantastic! You're more than halfway there! 🌟\";\n    } else if (this.progressPercentage < 100) {\n      return \"Almost done! Just a few more details to go! 🎯\";\n    } else {\n      return \"Perfect! Your profile is complete and ready to shine! ✨\";\n    }\n  }\n\n  getProgressColor(): string {\n    if (this.progressPercentage < 25) return '#ef4444'; // red\n    if (this.progressPercentage < 50) return '#f97316'; // orange\n    if (this.progressPercentage < 75) return '#eab308'; // yellow\n    if (this.progressPercentage < 100) return '#22c55e'; // green\n    return '#10b981'; // emerald\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAAiCC,UAAU,QAAQ,gBAAgB;AAW5D,WAAMC,0BAA0B,GAAhC,MAAMA,0BAA0B;EAcrCC,YACUC,EAAe,EACfC,WAA4B,EAC5BC,WAAwB,EACxBC,MAAc;IAHd,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAhBhB,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAC,kBAAkB,GAAW,CAAC;IAC9B,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,OAAO,GAAG,EAAE;IACZ,KAAAC,KAAK,GAAG,EAAE;IACV,KAAAC,YAAY,GAAgB,IAAI;IAChC,KAAAC,UAAU,GAAgC,IAAI;IAE9C;IACA,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,UAAU,GAAG,CAAC;IAQZ,IAAI,CAACC,WAAW,GAAG,IAAI,CAACb,EAAE,CAACc,KAAK,CAAC;MAC/BC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAClB,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACoB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACrB,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACoB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DE,WAAW,EAAE,CAAC,EAAE,EAAEtB,UAAU,CAACmB,QAAQ,CAAC;MACtCI,WAAW,EAAE,CAAC,EAAE,EAAE,CAACvB,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACwB,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC;MAC/EC,UAAU,EAAE,CAAC,EAAE,EAAEzB,UAAU,CAACmB,QAAQ,CAAC;MACrCO,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC3B,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACoB,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1DQ,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACvB,WAAW,GAAG,IAAI,CAACH,WAAW,CAAC2B,cAAc,EAAE;IACpD,IAAI,CAAC,IAAI,CAACxB,WAAW,EAAE;MACrB,IAAI,CAACD,MAAM,CAAC0B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGF;IACA,IAAI,IAAI,CAACzB,WAAW,CAAC0B,iBAAiB,EAAE;MACtC,IAAI,CAAC3B,MAAM,CAAC0B,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MAC3B;;IAGF,IAAI,CAACE,iBAAiB,EAAE;IACxB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC5B,WAAW,EAAE;MACpB,IAAI,CAACS,WAAW,CAACoB,UAAU,CAAC;QAC1BlB,SAAS,EAAE,IAAI,CAACX,WAAW,CAACW,SAAS,IAAI,EAAE;QAC3CG,QAAQ,EAAE,IAAI,CAACd,WAAW,CAACc,QAAQ,IAAI,EAAE;QACzCC,WAAW,EAAE,IAAI,CAACf,WAAW,CAACe,WAAW,IAAI,EAAE;QAC/CC,WAAW,EAAE,IAAI,CAAChB,WAAW,CAACgB,WAAW,IAAI,EAAE;QAC/CE,UAAU,EAAE,IAAI,CAAClB,WAAW,CAACkB,UAAU,IAAI,EAAE;QAC7CC,QAAQ,EAAE,IAAI,CAACnB,WAAW,CAACmB,QAAQ,IAAI,EAAE;QACzCC,GAAG,EAAE,IAAI,CAACpB,WAAW,CAACoB,GAAG,IAAI,EAAE;QAC/BC,OAAO,EAAE,IAAI,CAACrB,WAAW,CAACqB,OAAO,IAAI,EAAE;QACvCC,MAAM,EAAE,IAAI,CAACtB,WAAW,CAACsB,MAAM,EAAEQ,IAAI,CAAC,IAAI,CAAC,IAAI;OAChD,CAAC;;EAEN;EAEAH,iBAAiBA,CAAA;IACf,MAAMI,UAAU,GAAG,IAAI,CAACtB,WAAW,CAACuB,KAAK;IACzC,MAAMC,cAAc,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,KAAK,CAAC;IACnG,MAAMC,cAAc,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC;IAExD,IAAIC,iBAAiB,GAAG,CAAC;IACzB,IAAIC,iBAAiB,GAAG,CAAC;IAEzB;IACAH,cAAc,CAACI,OAAO,CAACC,KAAK,IAAG;MAC7B,IAAIP,UAAU,CAACO,KAAK,CAAC,IAAIP,UAAU,CAACO,KAAK,CAAC,CAACC,QAAQ,EAAE,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QACnEL,iBAAiB,EAAE;;IAEvB,CAAC,CAAC;IAEF;IACAD,cAAc,CAACG,OAAO,CAACC,KAAK,IAAG;MAC7B,IAAIP,UAAU,CAACO,KAAK,CAAC,IAAIP,UAAU,CAACO,KAAK,CAAC,CAACC,QAAQ,EAAE,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QACnEJ,iBAAiB,EAAE;;IAEvB,CAAC,CAAC;IAEF;IACA,IAAIK,eAAe,GAAG,CAAC;IACvB,IAAI,IAAI,CAACpC,YAAY,IAAK,IAAI,CAACL,WAAW,EAAE0C,YAAY,IAAI,IAAI,CAAC1C,WAAW,CAAC0C,YAAY,KAAK,qBAAsB,EAAE;MACpHD,eAAe,GAAG,CAAC;;IAGrB;IACA,MAAME,kBAAkB,GAAIR,iBAAiB,GAAGF,cAAc,CAACW,MAAM,GAAI,EAAE;IAC3E,MAAMC,kBAAkB,GAAIT,iBAAiB,GAAGF,cAAc,CAACU,MAAM,GAAI,EAAE;IAC3E,MAAME,eAAe,GAAGL,eAAe,GAAG,EAAE;IAE5C,IAAI,CAACxC,kBAAkB,GAAG8C,IAAI,CAACC,KAAK,CAACL,kBAAkB,GAAGE,kBAAkB,GAAGC,eAAe,CAAC;EACjG;EAEAG,cAAcA,CAACC,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAAC9C,YAAY,GAAG8C,IAAI;MAExB;MACA,MAAMG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,IAAI,CAAClD,UAAU,GAAGgD,MAAM,CAACG,MAAM;QAC/B,IAAI,CAAC9B,iBAAiB,EAAE;MAC1B,CAAC;MACD2B,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;;EAE9B;EAEAQ,QAAQA,CAAA;IACN,IAAI,IAAI,CAACpD,WAAW,GAAG,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACD,WAAW,EAAE;;EAEtB;EAEAqD,YAAYA,CAAA;IACV,IAAI,IAAI,CAACrD,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;EAEAsD,QAAQA,CAAA;IACN,IAAI,IAAI,CAACpD,WAAW,CAACqD,OAAO,EAAE;MAC5B,IAAI,CAACC,oBAAoB,EAAE;MAC3B;;IAGF,IAAI,CAAC7D,SAAS,GAAG,IAAI;IACrB,IAAI,CAACE,KAAK,GAAG,EAAE;IACf,IAAI,CAACD,OAAO,GAAG,EAAE;IAEjB,MAAM6D,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAE/B;IACAC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1D,WAAW,CAACuB,KAAK,CAAC,CAACK,OAAO,CAAC+B,GAAG,IAAG;MAChD,MAAMpC,KAAK,GAAG,IAAI,CAACvB,WAAW,CAACuB,KAAK,CAACoC,GAAG,CAAC;MACzC,IAAIA,GAAG,KAAK,QAAQ,IAAIpC,KAAK,EAAE;QAC7B;QACA,MAAMqC,WAAW,GAAGrC,KAAK,CAACsC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,KAAa,IAAKA,KAAK,CAAChC,IAAI,EAAE,CAAC,CAACiC,MAAM,CAAED,KAAa,IAAKA,KAAK,CAAC;QAC1GR,QAAQ,CAACU,MAAM,CAACN,GAAG,EAAEO,IAAI,CAACC,SAAS,CAACP,WAAW,CAAC,CAAC;OAClD,MAAM,IAAIrC,KAAK,EAAE;QAChBgC,QAAQ,CAACU,MAAM,CAACN,GAAG,EAAEpC,KAAK,CAAC;;IAE/B,CAAC,CAAC;IAEF;IACA,IAAI,IAAI,CAAC3B,YAAY,EAAE;MACrB2D,QAAQ,CAACU,MAAM,CAAC,OAAO,EAAE,IAAI,CAACrE,YAAY,CAAC;;IAG7C,IAAI,CAACP,WAAW,CAAC+E,eAAe,CAACb,QAAQ,CAAC,CAACc,SAAS,CAAC;MACnDC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAAC9E,SAAS,GAAG,KAAK;QACtB,IAAI,CAACC,OAAO,GAAG,iCAAiC;QAEhD;QACA,IAAI,CAACN,WAAW,CAACoF,cAAc,CAACD,QAAQ,CAACE,IAAI,CAAC;QAE9C;QACAC,UAAU,CAAC,MAAK;UACd,IAAI,CAACpF,MAAM,CAAC0B,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDrB,KAAK,EAAGgF,GAAG,IAAI;QACb,IAAI,CAAClF,SAAS,GAAG,KAAK;QACtB,IAAI,CAACE,KAAK,GAAGgF,GAAG,CAAChF,KAAK,EAAED,OAAO,IAAI,kDAAkD;MACvF;KACD,CAAC;EACJ;EAEAkF,UAAUA,CAAA;IACR;IACA,IAAIC,OAAO,CAAC,qGAAqG,CAAC,EAAE;MAClH,IAAI,CAACvF,MAAM,CAAC0B,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;;EAE/B;EAEQsC,oBAAoBA,CAAA;IAC1BG,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1D,WAAW,CAAC8E,QAAQ,CAAC,CAAClD,OAAO,CAAC+B,GAAG,IAAG;MACnD,IAAI,CAAC3D,WAAW,CAAC+E,GAAG,CAACpB,GAAG,CAAC,EAAEqB,aAAa,EAAE;IAC5C,CAAC,CAAC;EACJ;EAEA;EACAC,aAAaA,CAACC,SAAiB;IAC7B,MAAMrD,KAAK,GAAG,IAAI,CAAC7B,WAAW,CAAC+E,GAAG,CAACG,SAAS,CAAC;IAC7C,IAAIrD,KAAK,EAAEsD,MAAM,IAAItD,KAAK,CAACuD,OAAO,EAAE;MAClC,IAAIvD,KAAK,CAACsD,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,GAAGD,SAAS,cAAc;MAC/D,IAAIrD,KAAK,CAACsD,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAGD,SAAS,eAAe;MACjE,IAAIrD,KAAK,CAACsD,MAAM,CAAC,SAAS,CAAC,EAAE,OAAO,GAAGD,SAAS,oBAAoB;;IAEtE,OAAO,EAAE;EACX;EAEAG,cAAcA,CAACH,SAAiB;IAC9B,MAAMrD,KAAK,GAAG,IAAI,CAAC7B,WAAW,CAAC+E,GAAG,CAACG,SAAS,CAAC;IAC7C,OAAO,CAAC,EAAErD,KAAK,EAAEwB,OAAO,IAAIxB,KAAK,CAACuD,OAAO,CAAC;EAC5C;EAEAE,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAAC9F,kBAAkB,GAAG,EAAE,EAAE;MAChC,OAAO,4DAA4D;KACpE,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE;MACvC,OAAO,kDAAkD;KAC1D,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE;MACvC,OAAO,+CAA+C;KACvD,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,GAAG,EAAE;MACxC,OAAO,gDAAgD;KACxD,MAAM;MACL,OAAO,yDAAyD;;EAEpE;EAEA+F,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC/F,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,GAAG,EAAE,OAAO,SAAS,CAAC,CAAC;IACrD,OAAO,SAAS,CAAC,CAAC;EACpB;CACD;;AApOYP,0BAA0B,GAAAuG,UAAA,EALtCzG,SAAS,CAAC;EACT0G,QAAQ,EAAE,wBAAwB;EAClCC,WAAW,EAAE,qCAAqC;EAClDC,SAAS,EAAE,CAAC,oCAAoC;CACjD,CAAC,C,EACW1G,0BAA0B,CAoOtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}