const mongoose = require('mongoose');
const { logger } = require('../utils/logger');

// Options de connexion MongoDB
const options = {
  serverSelectionTimeoutMS: 5000, // Timeout après 5 secondes
  socketTimeoutMS: 45000, // Timeout après 45 secondes
  family: 4, // Utiliser IPv4, éviter les problèmes avec IPv6
  maxPoolSize: 10, // Limiter le nombre de connexions simultanées
  connectTimeoutMS: 10000, // Timeout de connexion après 10 secondes
};

// Fonction pour se connecter à MongoDB
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGO_URI || 'mongodb://127.0.0.1:27017/project_management';

    // Console logs pour debugging
    console.log('🔄 [Database] Starting MongoDB connection...');
    console.log(`🔗 [Database] Connection URI: ${mongoURI.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')}`);
    console.log('⚙️ [Database] Connection options:', JSON.stringify(options, null, 2));

    // Tentative de connexion
    logger.info(`[Database] Connecting to MongoDB: ${mongoURI.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')}`);

    const conn = await mongoose.connect(mongoURI, options);

    // Success messages
    console.log('✅ [Database] MongoDB Atlas connection SUCCESSFUL!');
    console.log(`🌐 [Database] Connected to host: ${conn.connection.host}`);
    console.log(`📊 [Database] Database name: ${conn.connection.name}`);
    console.log(`🔌 [Database] Connection state: ${getConnectionState(conn.connection.readyState)}`);

    logger.info(`[Database] MongoDB Connected: ${conn.connection.host}`);

    // Gérer les événements de connexion
    mongoose.connection.on('error', (err) => {
      console.error('❌ [Database] MongoDB connection error:', err.message);
      logger.error(`[Database] MongoDB connection error: ${err.message}`, { stack: err.stack });
    });

    mongoose.connection.on('disconnected', () => {
      console.warn('⚠️ [Database] MongoDB disconnected, attempting to reconnect...');
      logger.warn('[Database] MongoDB disconnected, attempting to reconnect...');
      setTimeout(connectDB, 5000); // Tenter de se reconnecter après 5 secondes
    });

    mongoose.connection.on('reconnected', () => {
      console.log('🔄 [Database] MongoDB reconnected successfully');
      logger.info('[Database] MongoDB reconnected successfully');
    });

    mongoose.connection.on('connected', () => {
      console.log('🔗 [Database] Mongoose connected to MongoDB');
    });

    mongoose.connection.on('connecting', () => {
      console.log('🔄 [Database] Mongoose connecting to MongoDB...');
    });

    // Intercepter les erreurs non gérées
    process.on('SIGINT', async () => {
      console.log('🛑 [Database] Received SIGINT, closing MongoDB connection...');
      await mongoose.connection.close();
      console.log('✅ [Database] MongoDB connection closed gracefully');
      logger.info('[Database] MongoDB connection closed due to app termination');
      process.exit(0);
    });

    return conn;
  } catch (error) {
    console.error('❌ [Database] MongoDB Atlas connection FAILED!');
    console.error('🔍 [Database] Error details:', {
      message: error.message,
      code: error.code,
      codeName: error.codeName,
      name: error.name
    });

    logger.error(`[Database] MongoDB connection error: ${error.message}`, {
      stack: error.stack,
      mongoURI: process.env.MONGO_URI ? process.env.MONGO_URI.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@') : 'undefined'
    });

    // Réessayer après un délai
    console.log('🔄 [Database] Retrying connection in 5 seconds...');
    logger.info('[Database] Retrying connection in 5 seconds...');
    setTimeout(connectDB, 5000);

    // Si nous sommes en production, ne pas planter le serveur
    if (process.env.NODE_ENV === 'production') {
      return null;
    } else {
      // En développement, laisser l'erreur se propager pour un feedback immédiat
      throw error;
    }
  }
};

// Fonction pour vérifier l'état de la connexion
const checkConnection = () => {
  return {
    isConnected: mongoose.connection.readyState === 1,
    state: getConnectionState(mongoose.connection.readyState),
    db: mongoose.connection.db?.databaseName || null,
  };
};

// Fonction pour obtenir l'état de la connexion sous forme de texte
const getConnectionState = (state) => {
  const states = {
    0: 'disconnected',
    1: 'connected',
    2: 'connecting',
    3: 'disconnecting',
    99: 'uninitialized',
  };
  return states[state] || 'unknown';
};

module.exports = {
  connectDB,
  checkConnection,
  mongoose,
};
