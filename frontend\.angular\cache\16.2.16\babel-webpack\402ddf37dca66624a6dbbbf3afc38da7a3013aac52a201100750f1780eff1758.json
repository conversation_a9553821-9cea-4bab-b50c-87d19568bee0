{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"src/app/services/data.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = function (a0, a1) {\n  return {\n    \"bg-white text-[#4f5fad] border-white\": a0,\n    \"border-white border-opacity-50 text-white\": a1\n  };\n};\nfunction ProfileCompletionComponent_ng_container_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const step_r7 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c0, ctx_r0.currentStep >= step_r7, ctx_r0.currentStep < step_r7));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", step_r7, \" \");\n  }\n}\nfunction ProfileCompletionComponent_div_25_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.getFieldError(\"firstName\"), \" \");\n  }\n}\nfunction ProfileCompletionComponent_div_25_p_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getFieldError(\"lastName\"), \" \");\n  }\n}\nfunction ProfileCompletionComponent_div_25_p_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.getFieldError(\"dateOfBirth\"), \" \");\n  }\n}\nfunction ProfileCompletionComponent_div_25_p_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.getFieldError(\"phoneNumber\"), \" \");\n  }\n}\nfunction ProfileCompletionComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"h3\", 28);\n    i0.ɵɵtext(2, \" Basic Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29)(4, \"div\")(5, \"label\", 30);\n    i0.ɵɵtext(6, \" First Name * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 31);\n    i0.ɵɵlistener(\"input\", function ProfileCompletionComponent_div_25_Template_input_input_7_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.calculateProgress());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ProfileCompletionComponent_div_25_p_8_Template, 2, 1, \"p\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\")(10, \"label\", 30);\n    i0.ɵɵtext(11, \" Last Name * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 33);\n    i0.ɵɵlistener(\"input\", function ProfileCompletionComponent_div_25_Template_input_input_12_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.calculateProgress());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, ProfileCompletionComponent_div_25_p_13_Template, 2, 1, \"p\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\")(15, \"label\", 30);\n    i0.ɵɵtext(16, \" Date of Birth * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 34);\n    i0.ɵɵlistener(\"change\", function ProfileCompletionComponent_div_25_Template_input_change_17_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.calculateProgress());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, ProfileCompletionComponent_div_25_p_18_Template, 2, 1, \"p\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\")(20, \"label\", 30);\n    i0.ɵɵtext(21, \" Phone Number * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"input\", 35);\n    i0.ɵɵlistener(\"input\", function ProfileCompletionComponent_div_25_Template_input_input_22_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.calculateProgress());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, ProfileCompletionComponent_div_25_p_23_Template, 2, 1, \"p\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r1.isFieldInvalid(\"firstName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFieldError(\"firstName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r1.isFieldInvalid(\"lastName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFieldError(\"lastName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r1.isFieldInvalid(\"dateOfBirth\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFieldError(\"dateOfBirth\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r1.isFieldInvalid(\"phoneNumber\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFieldError(\"phoneNumber\"));\n  }\n}\nfunction ProfileCompletionComponent_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function ProfileCompletionComponent_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.previousStep());\n    });\n    i0.ɵɵtext(1, \" Previous \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileCompletionComponent_button_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function ProfileCompletionComponent_button_32_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.nextStep());\n    });\n    i0.ɵɵtext(1, \" Next \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileCompletionComponent_button_33_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Complete Profile\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileCompletionComponent_button_33_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 42);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 43);\n    i0.ɵɵelement(2, \"circle\", 44)(3, \"path\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Completing... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileCompletionComponent_button_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵtemplate(1, ProfileCompletionComponent_button_33_span_1_Template, 2, 0, \"span\", 40);\n    i0.ɵɵtemplate(2, ProfileCompletionComponent_button_33_span_2_Template, 5, 0, \"span\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isLoading);\n  }\n}\nfunction ProfileCompletionComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"p\", 47);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.message);\n  }\n}\nfunction ProfileCompletionComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"p\", 49);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.error);\n  }\n}\nconst _c1 = function () {\n  return [1, 2, 3];\n};\nexport class ProfileCompletionComponent {\n  constructor(fb, authService, dataService, router) {\n    this.fb = fb;\n    this.authService = authService;\n    this.dataService = dataService;\n    this.router = router;\n    this.currentUser = null;\n    this.progressPercentage = 0;\n    this.isLoading = false;\n    this.message = '';\n    this.error = '';\n    this.selectedFile = null;\n    this.previewUrl = null;\n    // Form steps for better UX\n    this.currentStep = 1;\n    this.totalSteps = 3;\n    this.profileForm = this.fb.group({\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      dateOfBirth: ['', Validators.required],\n      phoneNumber: ['', [Validators.required, Validators.pattern(/^[0-9+\\-\\s()]+$/)]],\n      department: ['', Validators.required],\n      position: [''],\n      bio: ['', [Validators.required, Validators.minLength(10)]],\n      address: [''],\n      skills: ['']\n    });\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.getCurrentUser();\n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    // Check if profile is already complete\n    if (this.currentUser.isProfileComplete) {\n      this.router.navigate(['/']);\n      return;\n    }\n    this.calculateProgress();\n    this.prefillForm();\n  }\n  prefillForm() {\n    if (this.currentUser) {\n      this.profileForm.patchValue({\n        firstName: this.currentUser.firstName || '',\n        lastName: this.currentUser.lastName || '',\n        dateOfBirth: this.currentUser.dateOfBirth || '',\n        phoneNumber: this.currentUser.phoneNumber || '',\n        department: this.currentUser.department || '',\n        position: this.currentUser.position || '',\n        bio: this.currentUser.bio || '',\n        address: this.currentUser.address || '',\n        skills: this.currentUser.skills?.join(', ') || ''\n      });\n    }\n  }\n  calculateProgress() {\n    const formValues = this.profileForm.value;\n    const requiredFields = ['firstName', 'lastName', 'dateOfBirth', 'phoneNumber', 'department', 'bio'];\n    const optionalFields = ['position', 'address', 'skills'];\n    let completedRequired = 0;\n    let completedOptional = 0;\n    // Check required fields\n    requiredFields.forEach(field => {\n      if (formValues[field] && formValues[field].toString().trim() !== '') {\n        completedRequired++;\n      }\n    });\n    // Check optional fields\n    optionalFields.forEach(field => {\n      if (formValues[field] && formValues[field].toString().trim() !== '') {\n        completedOptional++;\n      }\n    });\n    // Check profile image\n    let hasProfileImage = 0;\n    if (this.selectedFile || this.currentUser?.profileImage && this.currentUser.profileImage !== 'uploads/default.png') {\n      hasProfileImage = 1;\n    }\n    // Calculate percentage: Required fields (60%) + Optional fields (30%) + Profile Image (10%)\n    const requiredPercentage = completedRequired / requiredFields.length * 60;\n    const optionalPercentage = completedOptional / optionalFields.length * 30;\n    const imagePercentage = hasProfileImage * 10;\n    this.progressPercentage = Math.round(requiredPercentage + optionalPercentage + imagePercentage);\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.selectedFile = file;\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = () => {\n        this.previewUrl = reader.result;\n        this.calculateProgress();\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  nextStep() {\n    if (this.currentStep < this.totalSteps) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  onSubmit() {\n    if (this.profileForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    this.isLoading = true;\n    this.error = '';\n    this.message = '';\n    const formData = new FormData();\n    // Add form fields\n    Object.keys(this.profileForm.value).forEach(key => {\n      const value = this.profileForm.value[key];\n      if (key === 'skills' && value) {\n        // Convert skills string to array\n        const skillsArray = value.split(',').map(skill => skill.trim()).filter(skill => skill);\n        formData.append(key, JSON.stringify(skillsArray));\n      } else if (value) {\n        formData.append(key, value);\n      }\n    });\n    // Add profile image if selected\n    if (this.selectedFile) {\n      formData.append('image', this.selectedFile);\n    }\n    this.dataService.completeProfile(formData).subscribe({\n      next: response => {\n        this.isLoading = false;\n        this.message = 'Profile completed successfully!';\n        // Update current user\n        this.authService.setCurrentUser(response.user);\n        // Redirect to home after a short delay\n        setTimeout(() => {\n          this.router.navigate(['/']);\n        }, 2000);\n      },\n      error: err => {\n        this.isLoading = false;\n        this.error = err.error?.message || 'An error occurred while completing your profile.';\n      }\n    });\n  }\n  skipForNow() {\n    // Allow user to skip but warn them\n    if (confirm('Are you sure you want to skip profile completion? You can complete it later from your profile page.')) {\n      this.router.navigate(['/']);\n    }\n  }\n  markFormGroupTouched() {\n    Object.keys(this.profileForm.controls).forEach(key => {\n      this.profileForm.get(key)?.markAsTouched();\n    });\n  }\n  // Helper methods for template\n  getFieldError(fieldName) {\n    const field = this.profileForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['minlength']) return `${fieldName} is too short`;\n      if (field.errors['pattern']) return `${fieldName} format is invalid`;\n    }\n    return '';\n  }\n  isFieldInvalid(fieldName) {\n    const field = this.profileForm.get(fieldName);\n    return !!(field?.invalid && field.touched);\n  }\n  getMotivationalMessage() {\n    if (this.progressPercentage < 25) {\n      return \"Great start! Let's build your amazing profile together! 🚀\";\n    } else if (this.progressPercentage < 50) {\n      return \"You're making excellent progress! Keep going! 💪\";\n    } else if (this.progressPercentage < 75) {\n      return \"Fantastic! You're more than halfway there! 🌟\";\n    } else if (this.progressPercentage < 100) {\n      return \"Almost done! Just a few more details to go! 🎯\";\n    } else {\n      return \"Perfect! Your profile is complete and ready to shine! ✨\";\n    }\n  }\n  getProgressColor() {\n    if (this.progressPercentage < 25) return '#ef4444'; // red\n    if (this.progressPercentage < 50) return '#f97316'; // orange\n    if (this.progressPercentage < 75) return '#eab308'; // yellow\n    if (this.progressPercentage < 100) return '#22c55e'; // green\n    return '#10b981'; // emerald\n  }\n\n  static {\n    this.ɵfac = function ProfileCompletionComponent_Factory(t) {\n      return new (t || ProfileCompletionComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.DataService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileCompletionComponent,\n      selectors: [[\"app-profile-completion\"]],\n      decls: 36,\n      vars: 17,\n      consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-[#f8fafc]\", \"to-[#e2e8f0]\", \"dark:from-[#0f172a]\", \"dark:to-[#1e293b]\", \"py-8\", \"px-4\"], [1, \"max-w-4xl\", \"mx-auto\"], [1, \"text-center\", \"mb-8\"], [1, \"text-4xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"bg-clip-text\", \"text-transparent\", \"mb-4\"], [1, \"text-lg\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-6\"], [1, \"max-w-md\", \"mx-auto\", \"mb-6\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-2\"], [1, \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"w-full\", \"bg-[#e2e8f0]\", \"dark:bg-[#2a2a2a]\", \"rounded-full\", \"h-3\", \"overflow-hidden\"], [1, \"h-full\", \"rounded-full\", \"transition-all\", \"duration-500\", \"ease-out\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-2xl\", \"shadow-xl\", \"border\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"overflow-hidden\"], [1, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"p-6\"], [1, \"flex\", \"justify-between\", \"items-center\"], [1, \"flex\", \"space-x-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"text-white\", \"text-sm\"], [1, \"p-8\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"space-y-6\", 4, \"ngIf\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mt-8\", \"pt-6\", \"border-t\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\"], [\"type\", \"button\", \"class\", \"px-6 py-3 text-[#4f5fad] dark:text-[#6d78c9] border border-[#4f5fad] dark:border-[#6d78c9] rounded-lg hover:bg-[#4f5fad] hover:text-white dark:hover:bg-[#6d78c9] dark:hover:text-white transition-all\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"px-6\", \"py-3\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\", 3, \"click\"], [\"type\", \"button\", \"class\", \"px-6 py-3 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"submit\", \"class\", \"px-8 py-3 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all disabled:opacity-50 disabled:cursor-not-allowed\", 3, \"disabled\", 4, \"ngIf\"], [\"class\", \"mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\", 4, \"ngIf\"], [\"class\", \"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"justify-center\", \"w-10\", \"h-10\", \"rounded-full\", \"border-2\", \"transition-all\", 3, \"ngClass\"], [1, \"space-y-6\"], [1, \"text-xl\", \"font-semibold\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-4\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [\"type\", \"text\", \"formControlName\", \"firstName\", \"placeholder\", \"Enter your first name\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"input\"], [\"class\", \"text-red-500 text-sm mt-1\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"lastName\", \"placeholder\", \"Enter your last name\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"input\"], [\"type\", \"date\", \"formControlName\", \"dateOfBirth\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"change\"], [\"type\", \"tel\", \"formControlName\", \"phoneNumber\", \"placeholder\", \"Enter your phone number\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"input\"], [1, \"text-red-500\", \"text-sm\", \"mt-1\"], [\"type\", \"button\", 1, \"px-6\", \"py-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", \"rounded-lg\", \"hover:bg-[#4f5fad]\", \"hover:text-white\", \"dark:hover:bg-[#6d78c9]\", \"dark:hover:text-white\", \"transition-all\", 3, \"click\"], [\"type\", \"button\", 1, \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", 3, \"click\"], [\"type\", \"submit\", 1, \"px-8\", \"py-3\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", 3, \"disabled\"], [4, \"ngIf\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [1, \"flex\", \"items-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"-ml-1\", \"mr-3\", \"h-5\", \"w-5\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"], [1, \"mt-4\", \"p-4\", \"bg-green-50\", \"dark:bg-green-900/20\", \"border\", \"border-green-200\", \"dark:border-green-800\", \"rounded-lg\"], [1, \"text-green-800\", \"dark:text-green-200\"], [1, \"mt-4\", \"p-4\", \"bg-red-50\", \"dark:bg-red-900/20\", \"border\", \"border-red-200\", \"dark:border-red-800\", \"rounded-lg\"], [1, \"text-red-800\", \"dark:text-red-200\"]],\n      template: function ProfileCompletionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \" Complete Your Profile \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \" Help us get to know you better! Complete your profile to unlock all features. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"span\", 7);\n          i0.ɵɵtext(10, \"Progress\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"span\", 7);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 8);\n          i0.ɵɵelement(14, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p\", 10);\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 11)(18, \"div\", 12)(19, \"div\", 13)(20, \"div\", 14);\n          i0.ɵɵtemplate(21, ProfileCompletionComponent_ng_container_21_Template, 3, 5, \"ng-container\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 16);\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"form\", 17);\n          i0.ɵɵlistener(\"ngSubmit\", function ProfileCompletionComponent_Template_form_ngSubmit_24_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(25, ProfileCompletionComponent_div_25_Template, 24, 12, \"div\", 18);\n          i0.ɵɵelementStart(26, \"div\", 19)(27, \"div\");\n          i0.ɵɵtemplate(28, ProfileCompletionComponent_button_28_Template, 2, 0, \"button\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 14)(30, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function ProfileCompletionComponent_Template_button_click_30_listener() {\n            return ctx.skipForNow();\n          });\n          i0.ɵɵtext(31, \" Skip for now \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, ProfileCompletionComponent_button_32_Template, 2, 0, \"button\", 22);\n          i0.ɵɵtemplate(33, ProfileCompletionComponent_button_33_Template, 3, 3, \"button\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(34, ProfileCompletionComponent_div_34_Template, 3, 1, \"div\", 24);\n          i0.ɵɵtemplate(35, ProfileCompletionComponent_div_35_Template, 3, 1, \"div\", 25);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate1(\"\", ctx.progressPercentage, \"%\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"width\", ctx.progressPercentage, \"%\")(\"background-color\", ctx.getProgressColor());\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getMotivationalMessage(), \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(16, _c1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate2(\" Step \", ctx.currentStep, \" of \", ctx.totalSteps, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.profileForm);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < ctx.totalSteps);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === ctx.totalSteps);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.message);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  transition: width 0.5s ease-in-out;\\n}\\n\\n\\n\\n.step-content[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n\\ninput[type=\\\"file\\\"][_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\ninput[type=\\\"file\\\"][_ngcontent-%COMP%]::-webkit-file-upload-button {\\n  background: linear-gradient(135deg, #4f5fad, #7826b5);\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  margin-right: 12px;\\n  -webkit-transition: all 0.3s ease;\\n  transition: all 0.3s ease;\\n}\\n\\ninput[type=\\\"file\\\"][_ngcontent-%COMP%]::-webkit-file-upload-button:hover {\\n  background: linear-gradient(135deg, #3d4a85, #6a1b9a);\\n}\\n\\n\\n\\ninput[_ngcontent-%COMP%]:focus, textarea[_ngcontent-%COMP%]:focus, select[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(79, 95, 173, 0.1);\\n}\\n\\n\\n\\ntextarea[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\ntextarea[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n\\ntextarea[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #4f5fad;\\n  border-radius: 3px;\\n}\\n\\ntextarea[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #3d4a85;\\n}\\n\\n\\n\\n.dark[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #2a2a2a;\\n}\\n\\n.dark[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #6d78c9;\\n}\\n\\n.dark[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #5a67b8;\\n}\\n\\n\\n\\n.step-indicator[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n\\n.step-indicator.active[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n\\n\\n.btn-gradient[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4f5fad, #7826b5);\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-gradient[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #3d4a85, #6a1b9a);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(79, 95, 173, 0.3);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n.animate-spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n\\n\\n.profile-preview[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n\\n.profile-preview[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 4px 12px rgba(79, 95, 173, 0.2);\\n}\\n\\n\\n\\n.field-error[_ngcontent-%COMP%] {\\n  border-color: #ef4444 !important;\\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;\\n}\\n\\n.field-success[_ngcontent-%COMP%] {\\n  border-color: #22c55e !important;\\n  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1) !important;\\n}\\n\\n\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #4f5fad, #7826b5, #4f5fad);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_progressGradient 2s ease infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progressGradient {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .step-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  \\n  .grid-cols-2[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .flex-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .space-x-4[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]    + *[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n    margin-top: 1rem;\\n  }\\n}\\n\\n\\n\\ninput[type=\\\"checkbox\\\"][_ngcontent-%COMP%], input[type=\\\"radio\\\"][_ngcontent-%COMP%] {\\n  accent-color: #4f5fad;\\n}\\n\\n\\n\\n.tooltip[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.tooltip[_ngcontent-%COMP%]:hover::after {\\n  content: attr(data-tooltip);\\n  position: absolute;\\n  bottom: 100%;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: #1f2937;\\n  color: white;\\n  padding: 0.5rem;\\n  border-radius: 0.375rem;\\n  font-size: 0.875rem;\\n  white-space: nowrap;\\n  z-index: 10;\\n}\\n\\n\\n\\n.form-card[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n\\n.form-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_checkmark {\\n  0% {\\n    transform: scale(0);\\n  }\\n  50% {\\n    transform: scale(1.2);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n\\n.success-checkmark[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_checkmark 0.5s ease-in-out;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb2ZpbGUtY29tcGxldGlvbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLDJDQUEyQztBQUMzQztFQUNFLGtDQUFrQztBQUNwQzs7QUFFQSxzQ0FBc0M7QUFDdEM7RUFDRSxrQ0FBa0M7QUFDcEM7O0FBRUE7RUFDRTtJQUNFLFVBQVU7SUFDViwyQkFBMkI7RUFDN0I7RUFDQTtJQUNFLFVBQVU7SUFDVix3QkFBd0I7RUFDMUI7QUFDRjs7QUFFQSxrQ0FBa0M7QUFDbEM7RUFDRSxlQUFlO0FBQ2pCOztBQUVBO0VBQ0UscURBQXFEO0VBQ3JELFlBQVk7RUFDWixZQUFZO0VBQ1osaUJBQWlCO0VBQ2pCLGtCQUFrQjtFQUNsQixlQUFlO0VBQ2Ysa0JBQWtCO0VBQ2xCLGlDQUF5QjtFQUF6Qix5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSxxREFBcUQ7QUFDdkQ7O0FBRUEsMENBQTBDO0FBQzFDOzs7RUFHRSw0Q0FBNEM7QUFDOUM7O0FBRUEsa0NBQWtDO0FBQ2xDO0VBQ0UsVUFBVTtBQUNaOztBQUVBO0VBQ0UsbUJBQW1CO0VBQ25CLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQixrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxtQkFBbUI7QUFDckI7O0FBRUEsd0JBQXdCO0FBQ3hCO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBLDhCQUE4QjtBQUM5QjtFQUNFLHlCQUF5QjtBQUMzQjs7QUFFQTtFQUNFLHFCQUFxQjtBQUN2Qjs7QUFFQSx5QkFBeUI7QUFDekI7RUFDRSxxREFBcUQ7RUFDckQseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0UscURBQXFEO0VBQ3JELDJCQUEyQjtFQUMzQiw2Q0FBNkM7QUFDL0M7O0FBRUEsOEJBQThCO0FBQzlCO0VBQ0U7SUFDRSx1QkFBdUI7RUFDekI7RUFDQTtJQUNFLHlCQUF5QjtFQUMzQjtBQUNGOztBQUVBO0VBQ0Usa0NBQWtDO0FBQ3BDOztBQUVBLGtDQUFrQztBQUNsQztFQUNFLHlCQUF5QjtBQUMzQjs7QUFFQTtFQUNFLHNCQUFzQjtFQUN0Qiw2Q0FBNkM7QUFDL0M7O0FBRUEsNEJBQTRCO0FBQzVCO0VBQ0UsZ0NBQWdDO0VBQ2hDLHVEQUF1RDtBQUN6RDs7QUFFQTtFQUNFLGdDQUFnQztFQUNoQyx1REFBdUQ7QUFDekQ7O0FBRUEsb0NBQW9DO0FBQ3BDO0VBQ0UsNkRBQTZEO0VBQzdELDBCQUEwQjtFQUMxQiw0Q0FBNEM7QUFDOUM7O0FBRUE7RUFDRTtJQUNFLDJCQUEyQjtFQUM3QjtFQUNBO0lBQ0UsNEJBQTRCO0VBQzlCO0FBQ0Y7O0FBRUEsMkJBQTJCO0FBQzNCO0VBQ0U7SUFDRSxhQUFhO0VBQ2Y7O0VBRUE7SUFDRSwwQkFBMEI7RUFDNUI7O0VBRUE7SUFDRSxzQkFBc0I7RUFDeEI7O0VBRUE7SUFDRSxjQUFjO0lBQ2QsZ0JBQWdCO0VBQ2xCO0FBQ0Y7O0FBRUEsc0NBQXNDO0FBQ3RDOztFQUVFLHFCQUFxQjtBQUN2Qjs7QUFFQSxvQkFBb0I7QUFDcEI7RUFDRSxrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSwyQkFBMkI7RUFDM0Isa0JBQWtCO0VBQ2xCLFlBQVk7RUFDWixTQUFTO0VBQ1QsMkJBQTJCO0VBQzNCLG1CQUFtQjtFQUNuQixZQUFZO0VBQ1osZUFBZTtFQUNmLHVCQUF1QjtFQUN2QixtQkFBbUI7RUFDbkIsbUJBQW1CO0VBQ25CLFdBQVc7QUFDYjs7QUFFQSx1QkFBdUI7QUFDdkI7RUFDRSx5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSxxRkFBcUY7QUFDdkY7O0FBRUEsc0JBQXNCO0FBQ3RCO0VBQ0U7SUFDRSxtQkFBbUI7RUFDckI7RUFDQTtJQUNFLHFCQUFxQjtFQUN2QjtFQUNBO0lBQ0UsbUJBQW1CO0VBQ3JCO0FBQ0Y7O0FBRUE7RUFDRSxxQ0FBcUM7QUFDdkMiLCJmaWxlIjoicHJvZmlsZS1jb21wbGV0aW9uLmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBDdXN0b20gYW5pbWF0aW9ucyBmb3IgdGhlIHByb2dyZXNzIGJhciAqL1xuLnByb2dyZXNzLWJhciB7XG4gIHRyYW5zaXRpb246IHdpZHRoIDAuNXMgZWFzZS1pbi1vdXQ7XG59XG5cbi8qIFNtb290aCB0cmFuc2l0aW9ucyBmb3IgZm9ybSBzdGVwcyAqL1xuLnN0ZXAtY29udGVudCB7XG4gIGFuaW1hdGlvbjogZmFkZUluIDAuM3MgZWFzZS1pbi1vdXQ7XG59XG5cbkBrZXlmcmFtZXMgZmFkZUluIHtcbiAgZnJvbSB7XG4gICAgb3BhY2l0eTogMDtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMTBweCk7XG4gIH1cbiAgdG8ge1xuICAgIG9wYWNpdHk6IDE7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xuICB9XG59XG5cbi8qIEN1c3RvbSBzdHlsaW5nIGZvciBmaWxlIGlucHV0ICovXG5pbnB1dFt0eXBlPVwiZmlsZVwiXSB7XG4gIGN1cnNvcjogcG9pbnRlcjtcbn1cblxuaW5wdXRbdHlwZT1cImZpbGVcIl06Oi13ZWJraXQtZmlsZS11cGxvYWQtYnV0dG9uIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzRmNWZhZCwgIzc4MjZiNSk7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgYm9yZGVyOiBub25lO1xuICBwYWRkaW5nOiA4cHggMTZweDtcbiAgYm9yZGVyLXJhZGl1czogNnB4O1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIG1hcmdpbi1yaWdodDogMTJweDtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbn1cblxuaW5wdXRbdHlwZT1cImZpbGVcIl06Oi13ZWJraXQtZmlsZS11cGxvYWQtYnV0dG9uOmhvdmVyIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzNkNGE4NSwgIzZhMWI5YSk7XG59XG5cbi8qIEZvY3VzIHN0YXRlcyBmb3IgYmV0dGVyIGFjY2Vzc2liaWxpdHkgKi9cbmlucHV0OmZvY3VzLFxudGV4dGFyZWE6Zm9jdXMsXG5zZWxlY3Q6Zm9jdXMge1xuICBib3gtc2hhZG93OiAwIDAgMCAzcHggcmdiYSg3OSwgOTUsIDE3MywgMC4xKTtcbn1cblxuLyogQ3VzdG9tIHNjcm9sbGJhciBmb3IgdGV4dGFyZWEgKi9cbnRleHRhcmVhOjotd2Via2l0LXNjcm9sbGJhciB7XG4gIHdpZHRoOiA2cHg7XG59XG5cbnRleHRhcmVhOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7XG4gIGJhY2tncm91bmQ6ICNmMWYxZjE7XG4gIGJvcmRlci1yYWRpdXM6IDNweDtcbn1cblxudGV4dGFyZWE6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcbiAgYmFja2dyb3VuZDogIzRmNWZhZDtcbiAgYm9yZGVyLXJhZGl1czogM3B4O1xufVxuXG50ZXh0YXJlYTo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIge1xuICBiYWNrZ3JvdW5kOiAjM2Q0YTg1O1xufVxuXG4vKiBEYXJrIG1vZGUgc2Nyb2xsYmFyICovXG4uZGFyayB0ZXh0YXJlYTo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sge1xuICBiYWNrZ3JvdW5kOiAjMmEyYTJhO1xufVxuXG4uZGFyayB0ZXh0YXJlYTo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIge1xuICBiYWNrZ3JvdW5kOiAjNmQ3OGM5O1xufVxuXG4uZGFyayB0ZXh0YXJlYTo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIge1xuICBiYWNrZ3JvdW5kOiAjNWE2N2I4O1xufVxuXG4vKiBTdGVwIGluZGljYXRvciBhbmltYXRpb25zICovXG4uc3RlcC1pbmRpY2F0b3Ige1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xufVxuXG4uc3RlcC1pbmRpY2F0b3IuYWN0aXZlIHtcbiAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xufVxuXG4vKiBCdXR0b24gaG92ZXIgZWZmZWN0cyAqL1xuLmJ0bi1ncmFkaWVudCB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM0ZjVmYWQsICM3ODI2YjUpO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xufVxuXG4uYnRuLWdyYWRpZW50OmhvdmVyIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzNkNGE4NSwgIzZhMWI5YSk7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcbiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDc5LCA5NSwgMTczLCAwLjMpO1xufVxuXG4vKiBMb2FkaW5nIHNwaW5uZXIgYW5pbWF0aW9uICovXG5Aa2V5ZnJhbWVzIHNwaW4ge1xuICBmcm9tIHtcbiAgICB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKTtcbiAgfVxuICB0byB7XG4gICAgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTtcbiAgfVxufVxuXG4uYW5pbWF0ZS1zcGluIHtcbiAgYW5pbWF0aW9uOiBzcGluIDFzIGxpbmVhciBpbmZpbml0ZTtcbn1cblxuLyogUHJvZmlsZSBpbWFnZSBwcmV2aWV3IHN0eWxpbmcgKi9cbi5wcm9maWxlLXByZXZpZXcge1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xufVxuXG4ucHJvZmlsZS1wcmV2aWV3OmhvdmVyIHtcbiAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTtcbiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDc5LCA5NSwgMTczLCAwLjIpO1xufVxuXG4vKiBGb3JtIHZhbGlkYXRpb24gc3R5bGluZyAqL1xuLmZpZWxkLWVycm9yIHtcbiAgYm9yZGVyLWNvbG9yOiAjZWY0NDQ0ICFpbXBvcnRhbnQ7XG4gIGJveC1zaGFkb3c6IDAgMCAwIDNweCByZ2JhKDIzOSwgNjgsIDY4LCAwLjEpICFpbXBvcnRhbnQ7XG59XG5cbi5maWVsZC1zdWNjZXNzIHtcbiAgYm9yZGVyLWNvbG9yOiAjMjJjNTVlICFpbXBvcnRhbnQ7XG4gIGJveC1zaGFkb3c6IDAgMCAwIDNweCByZ2JhKDM0LCAxOTcsIDk0LCAwLjEpICFpbXBvcnRhbnQ7XG59XG5cbi8qIFByb2dyZXNzIGJhciBncmFkaWVudCBhbmltYXRpb24gKi9cbi5wcm9ncmVzcy1maWxsIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjNGY1ZmFkLCAjNzgyNmI1LCAjNGY1ZmFkKTtcbiAgYmFja2dyb3VuZC1zaXplOiAyMDAlIDEwMCU7XG4gIGFuaW1hdGlvbjogcHJvZ3Jlc3NHcmFkaWVudCAycyBlYXNlIGluZmluaXRlO1xufVxuXG5Aa2V5ZnJhbWVzIHByb2dyZXNzR3JhZGllbnQge1xuICAwJSB7XG4gICAgYmFja2dyb3VuZC1wb3NpdGlvbjogMjAwJSAwO1xuICB9XG4gIDEwMCUge1xuICAgIGJhY2tncm91bmQtcG9zaXRpb246IC0yMDAlIDA7XG4gIH1cbn1cblxuLyogUmVzcG9uc2l2ZSBhZGp1c3RtZW50cyAqL1xuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5zdGVwLWNvbnRlbnQge1xuICAgIHBhZGRpbmc6IDFyZW07XG4gIH1cbiAgXG4gIC5ncmlkLWNvbHMtMiB7XG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XG4gIH1cbiAgXG4gIC5mbGV4LXJvdyB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgfVxuICBcbiAgLnNwYWNlLXgtNCA+ICogKyAqIHtcbiAgICBtYXJnaW4tbGVmdDogMDtcbiAgICBtYXJnaW4tdG9wOiAxcmVtO1xuICB9XG59XG5cbi8qIEN1c3RvbSBjaGVja2JveCBhbmQgcmFkaW8gc3R5bGluZyAqL1xuaW5wdXRbdHlwZT1cImNoZWNrYm94XCJdLFxuaW5wdXRbdHlwZT1cInJhZGlvXCJdIHtcbiAgYWNjZW50LWNvbG9yOiAjNGY1ZmFkO1xufVxuXG4vKiBUb29sdGlwIHN0eWxpbmcgKi9cbi50b29sdGlwIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xufVxuXG4udG9vbHRpcDpob3Zlcjo6YWZ0ZXIge1xuICBjb250ZW50OiBhdHRyKGRhdGEtdG9vbHRpcCk7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgYm90dG9tOiAxMDAlO1xuICBsZWZ0OiA1MCU7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNTAlKTtcbiAgYmFja2dyb3VuZDogIzFmMjkzNztcbiAgY29sb3I6IHdoaXRlO1xuICBwYWRkaW5nOiAwLjVyZW07XG4gIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xuICBmb250LXNpemU6IDAuODc1cmVtO1xuICB3aGl0ZS1zcGFjZTogbm93cmFwO1xuICB6LWluZGV4OiAxMDtcbn1cblxuLyogQ2FyZCBob3ZlciBlZmZlY3RzICovXG4uZm9ybS1jYXJkIHtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbn1cblxuLmZvcm0tY2FyZDpob3ZlciB7XG4gIGJveC1zaGFkb3c6IDAgMjBweCAyNXB4IC01cHggcmdiYSgwLCAwLCAwLCAwLjEpLCAwIDEwcHggMTBweCAtNXB4IHJnYmEoMCwgMCwgMCwgMC4wNCk7XG59XG5cbi8qIFN1Y2Nlc3MgYW5pbWF0aW9uICovXG5Aa2V5ZnJhbWVzIGNoZWNrbWFyayB7XG4gIDAlIHtcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDApO1xuICB9XG4gIDUwJSB7XG4gICAgdHJhbnNmb3JtOiBzY2FsZSgxLjIpO1xuICB9XG4gIDEwMCUge1xuICAgIHRyYW5zZm9ybTogc2NhbGUoMSk7XG4gIH1cbn1cblxuLnN1Y2Nlc3MtY2hlY2ttYXJrIHtcbiAgYW5pbWF0aW9uOiBjaGVja21hcmsgMC41cyBlYXNlLWluLW91dDtcbn1cbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "ctx_r0", "currentStep", "step_r7", "ɵɵtextInterpolate1", "ctx_r9", "getFieldError", "ctx_r10", "ctx_r11", "ctx_r12", "ɵɵlistener", "ProfileCompletionComponent_div_25_Template_input_input_7_listener", "ɵɵrestoreView", "_r14", "ctx_r13", "ɵɵnextContext", "ɵɵresetView", "calculateProgress", "ɵɵtemplate", "ProfileCompletionComponent_div_25_p_8_Template", "ProfileCompletionComponent_div_25_Template_input_input_12_listener", "ctx_r15", "ProfileCompletionComponent_div_25_p_13_Template", "ProfileCompletionComponent_div_25_Template_input_change_17_listener", "ctx_r16", "ProfileCompletionComponent_div_25_p_18_Template", "ProfileCompletionComponent_div_25_Template_input_input_22_listener", "ctx_r17", "ProfileCompletionComponent_div_25_p_23_Template", "ɵɵclassProp", "ctx_r1", "isFieldInvalid", "ProfileCompletionComponent_button_28_Template_button_click_0_listener", "_r19", "ctx_r18", "previousStep", "ProfileCompletionComponent_button_32_Template_button_click_0_listener", "_r21", "ctx_r20", "nextStep", "ɵɵnamespaceSVG", "ɵɵelement", "ProfileCompletionComponent_button_33_span_1_Template", "ProfileCompletionComponent_button_33_span_2_Template", "ctx_r4", "isLoading", "ɵɵtextInterpolate", "ctx_r5", "message", "ctx_r6", "error", "ProfileCompletionComponent", "constructor", "fb", "authService", "dataService", "router", "currentUser", "progressPercentage", "selectedFile", "previewUrl", "totalSteps", "profileForm", "group", "firstName", "required", "<PERSON><PERSON><PERSON><PERSON>", "lastName", "dateOfBirth", "phoneNumber", "pattern", "department", "position", "bio", "address", "skills", "ngOnInit", "getCurrentUser", "navigate", "isProfileComplete", "prefillForm", "patchValue", "join", "formValues", "value", "requiredFields", "optionalFields", "completedRequired", "completedOptional", "for<PERSON>ach", "field", "toString", "trim", "hasProfileImage", "profileImage", "requiredPercentage", "length", "optionalPercentage", "imagePercentage", "Math", "round", "onFileSelected", "event", "file", "target", "files", "reader", "FileReader", "onload", "result", "readAsDataURL", "onSubmit", "invalid", "markFormGroupTouched", "formData", "FormData", "Object", "keys", "key", "skillsArray", "split", "map", "skill", "filter", "append", "JSON", "stringify", "completeProfile", "subscribe", "next", "response", "setCurrentUser", "user", "setTimeout", "err", "skipFor<PERSON>ow", "confirm", "controls", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "fieldName", "errors", "touched", "getMotivationalMessage", "getProgressColor", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthuserService", "i3", "DataService", "i4", "Router", "selectors", "decls", "vars", "consts", "template", "ProfileCompletionComponent_Template", "rf", "ctx", "ProfileCompletionComponent_ng_container_21_Template", "ProfileCompletionComponent_Template_form_ngSubmit_24_listener", "ProfileCompletionComponent_div_25_Template", "ProfileCompletionComponent_button_28_Template", "ProfileCompletionComponent_Template_button_click_30_listener", "ProfileCompletionComponent_button_32_Template", "ProfileCompletionComponent_button_33_Template", "ProfileCompletionComponent_div_34_Template", "ProfileCompletionComponent_div_35_Template", "ɵɵstyleProp", "ɵɵpureFunction0", "_c1", "ɵɵtextInterpolate2"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\profile-completion\\profile-completion.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\profile-completion\\profile-completion.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { DataService } from 'src/app/services/data.service';\nimport { User } from 'src/app/models/user.model';\n\n@Component({\n  selector: 'app-profile-completion',\n  templateUrl: './profile-completion.component.html',\n  styleUrls: ['./profile-completion.component.css']\n})\nexport class ProfileCompletionComponent implements OnInit {\n  profileForm: FormGroup;\n  currentUser: User | null = null;\n  progressPercentage: number = 0;\n  isLoading = false;\n  message = '';\n  error = '';\n  selectedFile: File | null = null;\n  previewUrl: string | ArrayBuffer | null = null;\n\n  // Form steps for better UX\n  currentStep = 1;\n  totalSteps = 3;\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthuserService,\n    private dataService: DataService,\n    private router: Router\n  ) {\n    this.profileForm = this.fb.group({\n      firstName: ['', [Validators.required, Validators.minLength(2)]],\n      lastName: ['', [Validators.required, Validators.minLength(2)]],\n      dateOfBirth: ['', Validators.required],\n      phoneNumber: ['', [Validators.required, Validators.pattern(/^[0-9+\\-\\s()]+$/)]],\n      department: ['', Validators.required],\n      position: [''],\n      bio: ['', [Validators.required, Validators.minLength(10)]],\n      address: [''],\n      skills: ['']\n    });\n  }\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.getCurrentUser();\n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n\n    // Check if profile is already complete\n    if (this.currentUser.isProfileComplete) {\n      this.router.navigate(['/']);\n      return;\n    }\n\n    this.calculateProgress();\n    this.prefillForm();\n  }\n\n  prefillForm(): void {\n    if (this.currentUser) {\n      this.profileForm.patchValue({\n        firstName: this.currentUser.firstName || '',\n        lastName: this.currentUser.lastName || '',\n        dateOfBirth: this.currentUser.dateOfBirth || '',\n        phoneNumber: this.currentUser.phoneNumber || '',\n        department: this.currentUser.department || '',\n        position: this.currentUser.position || '',\n        bio: this.currentUser.bio || '',\n        address: this.currentUser.address || '',\n        skills: this.currentUser.skills?.join(', ') || ''\n      });\n    }\n  }\n\n  calculateProgress(): void {\n    const formValues = this.profileForm.value;\n    const requiredFields = ['firstName', 'lastName', 'dateOfBirth', 'phoneNumber', 'department', 'bio'];\n    const optionalFields = ['position', 'address', 'skills'];\n    \n    let completedRequired = 0;\n    let completedOptional = 0;\n\n    // Check required fields\n    requiredFields.forEach(field => {\n      if (formValues[field] && formValues[field].toString().trim() !== '') {\n        completedRequired++;\n      }\n    });\n\n    // Check optional fields\n    optionalFields.forEach(field => {\n      if (formValues[field] && formValues[field].toString().trim() !== '') {\n        completedOptional++;\n      }\n    });\n\n    // Check profile image\n    let hasProfileImage = 0;\n    if (this.selectedFile || (this.currentUser?.profileImage && this.currentUser.profileImage !== 'uploads/default.png')) {\n      hasProfileImage = 1;\n    }\n\n    // Calculate percentage: Required fields (60%) + Optional fields (30%) + Profile Image (10%)\n    const requiredPercentage = (completedRequired / requiredFields.length) * 60;\n    const optionalPercentage = (completedOptional / optionalFields.length) * 30;\n    const imagePercentage = hasProfileImage * 10;\n\n    this.progressPercentage = Math.round(requiredPercentage + optionalPercentage + imagePercentage);\n  }\n\n  onFileSelected(event: any): void {\n    const file = event.target.files[0];\n    if (file) {\n      this.selectedFile = file;\n      \n      // Create preview\n      const reader = new FileReader();\n      reader.onload = () => {\n        this.previewUrl = reader.result;\n        this.calculateProgress();\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n\n  nextStep(): void {\n    if (this.currentStep < this.totalSteps) {\n      this.currentStep++;\n    }\n  }\n\n  previousStep(): void {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n\n  onSubmit(): void {\n    if (this.profileForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    this.isLoading = true;\n    this.error = '';\n    this.message = '';\n\n    const formData = new FormData();\n    \n    // Add form fields\n    Object.keys(this.profileForm.value).forEach(key => {\n      const value = this.profileForm.value[key];\n      if (key === 'skills' && value) {\n        // Convert skills string to array\n        const skillsArray = value.split(',').map((skill: string) => skill.trim()).filter((skill: string) => skill);\n        formData.append(key, JSON.stringify(skillsArray));\n      } else if (value) {\n        formData.append(key, value);\n      }\n    });\n\n    // Add profile image if selected\n    if (this.selectedFile) {\n      formData.append('image', this.selectedFile);\n    }\n\n    this.dataService.completeProfile(formData).subscribe({\n      next: (response: any) => {\n        this.isLoading = false;\n        this.message = 'Profile completed successfully!';\n        \n        // Update current user\n        this.authService.setCurrentUser(response.user);\n        \n        // Redirect to home after a short delay\n        setTimeout(() => {\n          this.router.navigate(['/']);\n        }, 2000);\n      },\n      error: (err) => {\n        this.isLoading = false;\n        this.error = err.error?.message || 'An error occurred while completing your profile.';\n      }\n    });\n  }\n\n  skipForNow(): void {\n    // Allow user to skip but warn them\n    if (confirm('Are you sure you want to skip profile completion? You can complete it later from your profile page.')) {\n      this.router.navigate(['/']);\n    }\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.profileForm.controls).forEach(key => {\n      this.profileForm.get(key)?.markAsTouched();\n    });\n  }\n\n  // Helper methods for template\n  getFieldError(fieldName: string): string {\n    const field = this.profileForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['minlength']) return `${fieldName} is too short`;\n      if (field.errors['pattern']) return `${fieldName} format is invalid`;\n    }\n    return '';\n  }\n\n  isFieldInvalid(fieldName: string): boolean {\n    const field = this.profileForm.get(fieldName);\n    return !!(field?.invalid && field.touched);\n  }\n\n  getMotivationalMessage(): string {\n    if (this.progressPercentage < 25) {\n      return \"Great start! Let's build your amazing profile together! 🚀\";\n    } else if (this.progressPercentage < 50) {\n      return \"You're making excellent progress! Keep going! 💪\";\n    } else if (this.progressPercentage < 75) {\n      return \"Fantastic! You're more than halfway there! 🌟\";\n    } else if (this.progressPercentage < 100) {\n      return \"Almost done! Just a few more details to go! 🎯\";\n    } else {\n      return \"Perfect! Your profile is complete and ready to shine! ✨\";\n    }\n  }\n\n  getProgressColor(): string {\n    if (this.progressPercentage < 25) return '#ef4444'; // red\n    if (this.progressPercentage < 50) return '#f97316'; // orange\n    if (this.progressPercentage < 75) return '#eab308'; // yellow\n    if (this.progressPercentage < 100) return '#22c55e'; // green\n    return '#10b981'; // emerald\n  }\n}\n", "<div class=\"min-h-screen bg-gradient-to-br from-[#f8fafc] to-[#e2e8f0] dark:from-[#0f172a] dark:to-[#1e293b] py-8 px-4\">\n  <div class=\"max-w-4xl mx-auto\">\n    <!-- Header -->\n    <div class=\"text-center mb-8\">\n      <h1 class=\"text-4xl font-bold bg-gradient-to-r from-[#4f5fad] to-[#7826b5] bg-clip-text text-transparent mb-4\">\n        Complete Your Profile\n      </h1>\n      <p class=\"text-lg text-[#6d6870] dark:text-[#a0a0a0] mb-6\">\n        Help us get to know you better! Complete your profile to unlock all features.\n      </p>\n\n      <!-- Progress Bar -->\n      <div class=\"max-w-md mx-auto mb-6\">\n        <div class=\"flex justify-between items-center mb-2\">\n          <span class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9]\">Progress</span>\n          <span class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9]\">{{ progressPercentage }}%</span>\n        </div>\n        <div class=\"w-full bg-[#e2e8f0] dark:bg-[#2a2a2a] rounded-full h-3 overflow-hidden\">\n          <div\n            class=\"h-full rounded-full transition-all duration-500 ease-out\"\n            [style.width.%]=\"progressPercentage\"\n            [style.background-color]=\"getProgressColor()\">\n          </div>\n        </div>\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mt-2\">\n          {{ getMotivationalMessage() }}\n        </p>\n      </div>\n    </div>\n\n    <!-- Main Form Card -->\n    <div class=\"bg-white dark:bg-[#1e1e1e] rounded-2xl shadow-xl border border-[#edf1f4] dark:border-[#2a2a2a] overflow-hidden\">\n      <!-- Step Indicator -->\n      <div class=\"bg-gradient-to-r from-[#4f5fad] to-[#7826b5] p-6\">\n        <div class=\"flex justify-between items-center\">\n          <div class=\"flex space-x-4\">\n            <ng-container *ngFor=\"let step of [1, 2, 3]; let i = index\">\n              <div\n                class=\"flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all\"\n                [ngClass]=\"{\n                  'bg-white text-[#4f5fad] border-white': currentStep >= step,\n                  'border-white border-opacity-50 text-white': currentStep < step\n                }\">\n                {{ step }}\n              </div>\n            </ng-container>\n          </div>\n          <div class=\"text-white text-sm\">\n            Step {{ currentStep }} of {{ totalSteps }}\n          </div>\n        </div>\n      </div>\n\n      <!-- Form Content -->\n      <form [formGroup]=\"profileForm\" (ngSubmit)=\"onSubmit()\" class=\"p-8\">\n        <!-- Step 1: Basic Information -->\n        <div *ngIf=\"currentStep === 1\" class=\"space-y-6\">\n          <h3 class=\"text-xl font-semibold text-[#4f5fad] dark:text-[#6d78c9] mb-4\">\n            Basic Information\n          </h3>\n\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <!-- First Name -->\n            <div>\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\n                First Name *\n              </label>\n              <input\n                type=\"text\"\n                formControlName=\"firstName\"\n                (input)=\"calculateProgress()\"\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n                [class.border-red-500]=\"isFieldInvalid('firstName')\"\n                placeholder=\"Enter your first name\">\n              <p *ngIf=\"getFieldError('firstName')\" class=\"text-red-500 text-sm mt-1\">\n                {{ getFieldError('firstName') }}\n              </p>\n            </div>\n\n            <!-- Last Name -->\n            <div>\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\n                Last Name *\n              </label>\n              <input\n                type=\"text\"\n                formControlName=\"lastName\"\n                (input)=\"calculateProgress()\"\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n                [class.border-red-500]=\"isFieldInvalid('lastName')\"\n                placeholder=\"Enter your last name\">\n              <p *ngIf=\"getFieldError('lastName')\" class=\"text-red-500 text-sm mt-1\">\n                {{ getFieldError('lastName') }}\n              </p>\n            </div>\n\n            <!-- Date of Birth -->\n            <div>\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\n                Date of Birth *\n              </label>\n              <input\n                type=\"date\"\n                formControlName=\"dateOfBirth\"\n                (change)=\"calculateProgress()\"\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n                [class.border-red-500]=\"isFieldInvalid('dateOfBirth')\">\n              <p *ngIf=\"getFieldError('dateOfBirth')\" class=\"text-red-500 text-sm mt-1\">\n                {{ getFieldError('dateOfBirth') }}\n              </p>\n            </div>\n\n            <!-- Phone Number -->\n            <div>\n              <label class=\"block text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\">\n                Phone Number *\n              </label>\n              <input\n                type=\"tel\"\n                formControlName=\"phoneNumber\"\n                (input)=\"calculateProgress()\"\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n                [class.border-red-500]=\"isFieldInvalid('phoneNumber')\"\n                placeholder=\"Enter your phone number\">\n              <p *ngIf=\"getFieldError('phoneNumber')\" class=\"text-red-500 text-sm mt-1\">\n                {{ getFieldError('phoneNumber') }}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <!-- Navigation Buttons -->\n        <div class=\"flex justify-between items-center mt-8 pt-6 border-t border-[#edf1f4] dark:border-[#2a2a2a]\">\n          <div>\n            <button\n              *ngIf=\"currentStep > 1\"\n              type=\"button\"\n              (click)=\"previousStep()\"\n              class=\"px-6 py-3 text-[#4f5fad] dark:text-[#6d78c9] border border-[#4f5fad] dark:border-[#6d78c9] rounded-lg hover:bg-[#4f5fad] hover:text-white dark:hover:bg-[#6d78c9] dark:hover:text-white transition-all\">\n              Previous\n            </button>\n          </div>\n\n          <div class=\"flex space-x-4\">\n            <button\n              type=\"button\"\n              (click)=\"skipForNow()\"\n              class=\"px-6 py-3 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\">\n              Skip for now\n            </button>\n\n            <button\n              *ngIf=\"currentStep < totalSteps\"\n              type=\"button\"\n              (click)=\"nextStep()\"\n              class=\"px-6 py-3 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all\">\n              Next\n            </button>\n\n            <button\n              *ngIf=\"currentStep === totalSteps\"\n              type=\"submit\"\n              [disabled]=\"isLoading\"\n              class=\"px-8 py-3 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all disabled:opacity-50 disabled:cursor-not-allowed\">\n              <span *ngIf=\"!isLoading\">Complete Profile</span>\n              <span *ngIf=\"isLoading\" class=\"flex items-center\">\n                <svg class=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                  <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n                  <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                </svg>\n                Completing...\n              </span>\n            </button>\n          </div>\n        </div>\n\n        <!-- Messages -->\n        <div *ngIf=\"message\" class=\"mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\">\n          <p class=\"text-green-800 dark:text-green-200\">{{ message }}</p>\n        </div>\n\n        <div *ngIf=\"error\" class=\"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\">\n          <p class=\"text-red-800 dark:text-red-200\">{{ error }}</p>\n        </div>\n      </form>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;ICmCvDC,EAAA,CAAAC,uBAAA,GAA4D;IAC1DD,EAAA,CAAAE,cAAA,cAKK;IACHF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACRJ,EAAA,CAAAK,qBAAA,EAAe;;;;;IANXL,EAAA,CAAAM,SAAA,GAGE;IAHFN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,WAAA,IAAAC,OAAA,EAAAF,MAAA,CAAAC,WAAA,GAAAC,OAAA,EAGE;IACFZ,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAa,kBAAA,MAAAD,OAAA,MACF;;;;;IA8BAZ,EAAA,CAAAE,cAAA,YAAwE;IACtEF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAa,kBAAA,MAAAC,MAAA,CAAAC,aAAA,mBACF;;;;;IAeAf,EAAA,CAAAE,cAAA,YAAuE;IACrEF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAa,kBAAA,MAAAG,OAAA,CAAAD,aAAA,kBACF;;;;;IAcAf,EAAA,CAAAE,cAAA,YAA0E;IACxEF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAa,kBAAA,MAAAI,OAAA,CAAAF,aAAA,qBACF;;;;;IAeAf,EAAA,CAAAE,cAAA,YAA0E;IACxEF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAa,kBAAA,MAAAK,OAAA,CAAAH,aAAA,qBACF;;;;;;IAtENf,EAAA,CAAAE,cAAA,cAAiD;IAE7CF,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAELJ,EAAA,CAAAE,cAAA,cAAmD;IAI7CF,EAAA,CAAAG,MAAA,qBACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAAE,cAAA,gBAMsC;IAHpCF,EAAA,CAAAmB,UAAA,mBAAAC,kEAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAF,OAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IAH/B1B,EAAA,CAAAI,YAAA,EAMsC;IACtCJ,EAAA,CAAA2B,UAAA,IAAAC,8CAAA,gBAEI;IACN5B,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,UAAK;IAEDF,EAAA,CAAAG,MAAA,qBACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAAE,cAAA,iBAMqC;IAHnCF,EAAA,CAAAmB,UAAA,mBAAAU,mEAAA;MAAA7B,EAAA,CAAAqB,aAAA,CAAAC,IAAA;MAAA,MAAAQ,OAAA,GAAA9B,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAK,OAAA,CAAAJ,iBAAA,EAAmB;IAAA,EAAC;IAH/B1B,EAAA,CAAAI,YAAA,EAMqC;IACrCJ,EAAA,CAAA2B,UAAA,KAAAI,+CAAA,gBAEI;IACN/B,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,WAAK;IAEDF,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAAE,cAAA,iBAKyD;IAFvDF,EAAA,CAAAmB,UAAA,oBAAAa,oEAAA;MAAAhC,EAAA,CAAAqB,aAAA,CAAAC,IAAA;MAAA,MAAAW,OAAA,GAAAjC,EAAA,CAAAwB,aAAA;MAAA,OAAUxB,EAAA,CAAAyB,WAAA,CAAAQ,OAAA,CAAAP,iBAAA,EAAmB;IAAA,EAAC;IAHhC1B,EAAA,CAAAI,YAAA,EAKyD;IACzDJ,EAAA,CAAA2B,UAAA,KAAAO,+CAAA,gBAEI;IACNlC,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,WAAK;IAEDF,EAAA,CAAAG,MAAA,wBACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAAE,cAAA,iBAMwC;IAHtCF,EAAA,CAAAmB,UAAA,mBAAAgB,mEAAA;MAAAnC,EAAA,CAAAqB,aAAA,CAAAC,IAAA;MAAA,MAAAc,OAAA,GAAApC,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAW,OAAA,CAAAV,iBAAA,EAAmB;IAAA,EAAC;IAH/B1B,EAAA,CAAAI,YAAA,EAMwC;IACxCJ,EAAA,CAAA2B,UAAA,KAAAU,+CAAA,gBAEI;IACNrC,EAAA,CAAAI,YAAA,EAAM;;;;IAvDFJ,EAAA,CAAAM,SAAA,GAAoD;IAApDN,EAAA,CAAAsC,WAAA,mBAAAC,MAAA,CAAAC,cAAA,cAAoD;IAElDxC,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAO,UAAA,SAAAgC,MAAA,CAAAxB,aAAA,cAAgC;IAelCf,EAAA,CAAAM,SAAA,GAAmD;IAAnDN,EAAA,CAAAsC,WAAA,mBAAAC,MAAA,CAAAC,cAAA,aAAmD;IAEjDxC,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAO,UAAA,SAAAgC,MAAA,CAAAxB,aAAA,aAA+B;IAejCf,EAAA,CAAAM,SAAA,GAAsD;IAAtDN,EAAA,CAAAsC,WAAA,mBAAAC,MAAA,CAAAC,cAAA,gBAAsD;IACpDxC,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAO,UAAA,SAAAgC,MAAA,CAAAxB,aAAA,gBAAkC;IAepCf,EAAA,CAAAM,SAAA,GAAsD;IAAtDN,EAAA,CAAAsC,WAAA,mBAAAC,MAAA,CAAAC,cAAA,gBAAsD;IAEpDxC,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAO,UAAA,SAAAgC,MAAA,CAAAxB,aAAA,gBAAkC;;;;;;IAUxCf,EAAA,CAAAE,cAAA,iBAIiN;IAD/MF,EAAA,CAAAmB,UAAA,mBAAAsB,sEAAA;MAAAzC,EAAA,CAAAqB,aAAA,CAAAqB,IAAA;MAAA,MAAAC,OAAA,GAAA3C,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAkB,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAExB5C,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;IAWTJ,EAAA,CAAAE,cAAA,iBAI8I;IAD5IF,EAAA,CAAAmB,UAAA,mBAAA0B,sEAAA;MAAA7C,EAAA,CAAAqB,aAAA,CAAAyB,IAAA;MAAA,MAAAC,OAAA,GAAA/C,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAsB,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAEpBhD,EAAA,CAAAG,MAAA,aACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IAOPJ,EAAA,CAAAE,cAAA,WAAyB;IAAAF,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAChDJ,EAAA,CAAAE,cAAA,eAAkD;IAChDF,EAAA,CAAAiD,cAAA,EAA2H;IAA3HjD,EAAA,CAAAE,cAAA,cAA2H;IACzHF,EAAA,CAAAkD,SAAA,iBAAkG;IAEpGlD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAZTJ,EAAA,CAAAE,cAAA,iBAI8L;IAC5LF,EAAA,CAAA2B,UAAA,IAAAwB,oDAAA,mBAAgD;IAChDnD,EAAA,CAAA2B,UAAA,IAAAyB,oDAAA,mBAMO;IACTpD,EAAA,CAAAI,YAAA,EAAS;;;;IAVPJ,EAAA,CAAAO,UAAA,aAAA8C,MAAA,CAAAC,SAAA,CAAsB;IAEftD,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAO,UAAA,UAAA8C,MAAA,CAAAC,SAAA,CAAgB;IAChBtD,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAO,UAAA,SAAA8C,MAAA,CAAAC,SAAA,CAAe;;;;;IAY5BtD,EAAA,CAAAE,cAAA,cAAgI;IAChFF,EAAA,CAAAG,MAAA,GAAa;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAAjBJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAuD,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAa;;;;;IAG7DzD,EAAA,CAAAE,cAAA,cAAsH;IAC1EF,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAAfJ,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAuD,iBAAA,CAAAG,MAAA,CAAAC,KAAA,CAAW;;;;;;AD1K/D,OAAM,MAAOC,0BAA0B;EAcrCC,YACUC,EAAe,EACfC,WAA4B,EAC5BC,WAAwB,EACxBC,MAAc;IAHd,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAhBhB,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAC,kBAAkB,GAAW,CAAC;IAC9B,KAAAb,SAAS,GAAG,KAAK;IACjB,KAAAG,OAAO,GAAG,EAAE;IACZ,KAAAE,KAAK,GAAG,EAAE;IACV,KAAAS,YAAY,GAAgB,IAAI;IAChC,KAAAC,UAAU,GAAgC,IAAI;IAE9C;IACA,KAAA1D,WAAW,GAAG,CAAC;IACf,KAAA2D,UAAU,GAAG,CAAC;IAQZ,IAAI,CAACC,WAAW,GAAG,IAAI,CAACT,EAAE,CAACU,KAAK,CAAC;MAC/BC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC1E,UAAU,CAAC2E,QAAQ,EAAE3E,UAAU,CAAC4E,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC7E,UAAU,CAAC2E,QAAQ,EAAE3E,UAAU,CAAC4E,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DE,WAAW,EAAE,CAAC,EAAE,EAAE9E,UAAU,CAAC2E,QAAQ,CAAC;MACtCI,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC/E,UAAU,CAAC2E,QAAQ,EAAE3E,UAAU,CAACgF,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC;MAC/EC,UAAU,EAAE,CAAC,EAAE,EAAEjF,UAAU,CAAC2E,QAAQ,CAAC;MACrCO,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,GAAG,EAAE,CAAC,EAAE,EAAE,CAACnF,UAAU,CAAC2E,QAAQ,EAAE3E,UAAU,CAAC4E,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1DQ,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACnB,WAAW,GAAG,IAAI,CAACH,WAAW,CAACuB,cAAc,EAAE;IACpD,IAAI,CAAC,IAAI,CAACpB,WAAW,EAAE;MACrB,IAAI,CAACD,MAAM,CAACsB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGF;IACA,IAAI,IAAI,CAACrB,WAAW,CAACsB,iBAAiB,EAAE;MACtC,IAAI,CAACvB,MAAM,CAACsB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MAC3B;;IAGF,IAAI,CAAC7D,iBAAiB,EAAE;IACxB,IAAI,CAAC+D,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,IAAI,CAACvB,WAAW,EAAE;MACpB,IAAI,CAACK,WAAW,CAACmB,UAAU,CAAC;QAC1BjB,SAAS,EAAE,IAAI,CAACP,WAAW,CAACO,SAAS,IAAI,EAAE;QAC3CG,QAAQ,EAAE,IAAI,CAACV,WAAW,CAACU,QAAQ,IAAI,EAAE;QACzCC,WAAW,EAAE,IAAI,CAACX,WAAW,CAACW,WAAW,IAAI,EAAE;QAC/CC,WAAW,EAAE,IAAI,CAACZ,WAAW,CAACY,WAAW,IAAI,EAAE;QAC/CE,UAAU,EAAE,IAAI,CAACd,WAAW,CAACc,UAAU,IAAI,EAAE;QAC7CC,QAAQ,EAAE,IAAI,CAACf,WAAW,CAACe,QAAQ,IAAI,EAAE;QACzCC,GAAG,EAAE,IAAI,CAAChB,WAAW,CAACgB,GAAG,IAAI,EAAE;QAC/BC,OAAO,EAAE,IAAI,CAACjB,WAAW,CAACiB,OAAO,IAAI,EAAE;QACvCC,MAAM,EAAE,IAAI,CAAClB,WAAW,CAACkB,MAAM,EAAEO,IAAI,CAAC,IAAI,CAAC,IAAI;OAChD,CAAC;;EAEN;EAEAjE,iBAAiBA,CAAA;IACf,MAAMkE,UAAU,GAAG,IAAI,CAACrB,WAAW,CAACsB,KAAK;IACzC,MAAMC,cAAc,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,KAAK,CAAC;IACnG,MAAMC,cAAc,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC;IAExD,IAAIC,iBAAiB,GAAG,CAAC;IACzB,IAAIC,iBAAiB,GAAG,CAAC;IAEzB;IACAH,cAAc,CAACI,OAAO,CAACC,KAAK,IAAG;MAC7B,IAAIP,UAAU,CAACO,KAAK,CAAC,IAAIP,UAAU,CAACO,KAAK,CAAC,CAACC,QAAQ,EAAE,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QACnEL,iBAAiB,EAAE;;IAEvB,CAAC,CAAC;IAEF;IACAD,cAAc,CAACG,OAAO,CAACC,KAAK,IAAG;MAC7B,IAAIP,UAAU,CAACO,KAAK,CAAC,IAAIP,UAAU,CAACO,KAAK,CAAC,CAACC,QAAQ,EAAE,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QACnEJ,iBAAiB,EAAE;;IAEvB,CAAC,CAAC;IAEF;IACA,IAAIK,eAAe,GAAG,CAAC;IACvB,IAAI,IAAI,CAAClC,YAAY,IAAK,IAAI,CAACF,WAAW,EAAEqC,YAAY,IAAI,IAAI,CAACrC,WAAW,CAACqC,YAAY,KAAK,qBAAsB,EAAE;MACpHD,eAAe,GAAG,CAAC;;IAGrB;IACA,MAAME,kBAAkB,GAAIR,iBAAiB,GAAGF,cAAc,CAACW,MAAM,GAAI,EAAE;IAC3E,MAAMC,kBAAkB,GAAIT,iBAAiB,GAAGF,cAAc,CAACU,MAAM,GAAI,EAAE;IAC3E,MAAME,eAAe,GAAGL,eAAe,GAAG,EAAE;IAE5C,IAAI,CAACnC,kBAAkB,GAAGyC,IAAI,CAACC,KAAK,CAACL,kBAAkB,GAAGE,kBAAkB,GAAGC,eAAe,CAAC;EACjG;EAEAG,cAAcA,CAACC,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAAC5C,YAAY,GAAG4C,IAAI;MAExB;MACA,MAAMG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,IAAI,CAAChD,UAAU,GAAG8C,MAAM,CAACG,MAAM;QAC/B,IAAI,CAAC5F,iBAAiB,EAAE;MAC1B,CAAC;MACDyF,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;;EAE9B;EAEAhE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACrC,WAAW,GAAG,IAAI,CAAC2D,UAAU,EAAE;MACtC,IAAI,CAAC3D,WAAW,EAAE;;EAEtB;EAEAiC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACjC,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;EAEA6G,QAAQA,CAAA;IACN,IAAI,IAAI,CAACjD,WAAW,CAACkD,OAAO,EAAE;MAC5B,IAAI,CAACC,oBAAoB,EAAE;MAC3B;;IAGF,IAAI,CAACpE,SAAS,GAAG,IAAI;IACrB,IAAI,CAACK,KAAK,GAAG,EAAE;IACf,IAAI,CAACF,OAAO,GAAG,EAAE;IAEjB,MAAMkE,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAE/B;IACAC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvD,WAAW,CAACsB,KAAK,CAAC,CAACK,OAAO,CAAC6B,GAAG,IAAG;MAChD,MAAMlC,KAAK,GAAG,IAAI,CAACtB,WAAW,CAACsB,KAAK,CAACkC,GAAG,CAAC;MACzC,IAAIA,GAAG,KAAK,QAAQ,IAAIlC,KAAK,EAAE;QAC7B;QACA,MAAMmC,WAAW,GAAGnC,KAAK,CAACoC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,KAAa,IAAKA,KAAK,CAAC9B,IAAI,EAAE,CAAC,CAAC+B,MAAM,CAAED,KAAa,IAAKA,KAAK,CAAC;QAC1GR,QAAQ,CAACU,MAAM,CAACN,GAAG,EAAEO,IAAI,CAACC,SAAS,CAACP,WAAW,CAAC,CAAC;OAClD,MAAM,IAAInC,KAAK,EAAE;QAChB8B,QAAQ,CAACU,MAAM,CAACN,GAAG,EAAElC,KAAK,CAAC;;IAE/B,CAAC,CAAC;IAEF;IACA,IAAI,IAAI,CAACzB,YAAY,EAAE;MACrBuD,QAAQ,CAACU,MAAM,CAAC,OAAO,EAAE,IAAI,CAACjE,YAAY,CAAC;;IAG7C,IAAI,CAACJ,WAAW,CAACwE,eAAe,CAACb,QAAQ,CAAC,CAACc,SAAS,CAAC;MACnDC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACrF,SAAS,GAAG,KAAK;QACtB,IAAI,CAACG,OAAO,GAAG,iCAAiC;QAEhD;QACA,IAAI,CAACM,WAAW,CAAC6E,cAAc,CAACD,QAAQ,CAACE,IAAI,CAAC;QAE9C;QACAC,UAAU,CAAC,MAAK;UACd,IAAI,CAAC7E,MAAM,CAACsB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACD5B,KAAK,EAAGoF,GAAG,IAAI;QACb,IAAI,CAACzF,SAAS,GAAG,KAAK;QACtB,IAAI,CAACK,KAAK,GAAGoF,GAAG,CAACpF,KAAK,EAAEF,OAAO,IAAI,kDAAkD;MACvF;KACD,CAAC;EACJ;EAEAuF,UAAUA,CAAA;IACR;IACA,IAAIC,OAAO,CAAC,qGAAqG,CAAC,EAAE;MAClH,IAAI,CAAChF,MAAM,CAACsB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;;EAE/B;EAEQmC,oBAAoBA,CAAA;IAC1BG,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvD,WAAW,CAAC2E,QAAQ,CAAC,CAAChD,OAAO,CAAC6B,GAAG,IAAG;MACnD,IAAI,CAACxD,WAAW,CAAC4E,GAAG,CAACpB,GAAG,CAAC,EAAEqB,aAAa,EAAE;IAC5C,CAAC,CAAC;EACJ;EAEA;EACArI,aAAaA,CAACsI,SAAiB;IAC7B,MAAMlD,KAAK,GAAG,IAAI,CAAC5B,WAAW,CAAC4E,GAAG,CAACE,SAAS,CAAC;IAC7C,IAAIlD,KAAK,EAAEmD,MAAM,IAAInD,KAAK,CAACoD,OAAO,EAAE;MAClC,IAAIpD,KAAK,CAACmD,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,GAAGD,SAAS,cAAc;MAC/D,IAAIlD,KAAK,CAACmD,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAGD,SAAS,eAAe;MACjE,IAAIlD,KAAK,CAACmD,MAAM,CAAC,SAAS,CAAC,EAAE,OAAO,GAAGD,SAAS,oBAAoB;;IAEtE,OAAO,EAAE;EACX;EAEA7G,cAAcA,CAAC6G,SAAiB;IAC9B,MAAMlD,KAAK,GAAG,IAAI,CAAC5B,WAAW,CAAC4E,GAAG,CAACE,SAAS,CAAC;IAC7C,OAAO,CAAC,EAAElD,KAAK,EAAEsB,OAAO,IAAItB,KAAK,CAACoD,OAAO,CAAC;EAC5C;EAEAC,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACrF,kBAAkB,GAAG,EAAE,EAAE;MAChC,OAAO,4DAA4D;KACpE,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE;MACvC,OAAO,kDAAkD;KAC1D,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE;MACvC,OAAO,+CAA+C;KACvD,MAAM,IAAI,IAAI,CAACA,kBAAkB,GAAG,GAAG,EAAE;MACxC,OAAO,gDAAgD;KACxD,MAAM;MACL,OAAO,yDAAyD;;EAEpE;EAEAsF,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACtF,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACpD,IAAI,IAAI,CAACA,kBAAkB,GAAG,GAAG,EAAE,OAAO,SAAS,CAAC,CAAC;IACrD,OAAO,SAAS,CAAC,CAAC;EACpB;;;;uBAnOWP,0BAA0B,EAAA5D,EAAA,CAAA0J,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5J,EAAA,CAAA0J,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA9J,EAAA,CAAA0J,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAhK,EAAA,CAAA0J,iBAAA,CAAAO,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA1BtG,0BAA0B;MAAAuG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZvCzK,EAAA,CAAAE,cAAA,aAAwH;UAKhHF,EAAA,CAAAG,MAAA,8BACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAE,cAAA,WAA2D;UACzDF,EAAA,CAAAG,MAAA,sFACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAGJJ,EAAA,CAAAE,cAAA,aAAmC;UAEsCF,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAO;UACpFJ,EAAA,CAAAE,cAAA,eAAqE;UAAAF,EAAA,CAAAG,MAAA,IAAyB;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAEvGJ,EAAA,CAAAE,cAAA,cAAoF;UAClFF,EAAA,CAAAkD,SAAA,cAIM;UACRlD,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAE,cAAA,aAA2D;UACzDF,EAAA,CAAAG,MAAA,IACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAKRJ,EAAA,CAAAE,cAAA,eAA4H;UAKpHF,EAAA,CAAA2B,UAAA,KAAAgJ,mDAAA,2BASe;UACjB3K,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAE,cAAA,eAAgC;UAC9BF,EAAA,CAAAG,MAAA,IACF;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAE,cAAA,gBAAoE;UAApCF,EAAA,CAAAmB,UAAA,sBAAAyJ,8DAAA;YAAA,OAAYF,GAAA,CAAAlD,QAAA,EAAU;UAAA,EAAC;UAErDxH,EAAA,CAAA2B,UAAA,KAAAkJ,0CAAA,oBAyEM;UAGN7K,EAAA,CAAAE,cAAA,eAAyG;UAErGF,EAAA,CAAA2B,UAAA,KAAAmJ,6CAAA,qBAMS;UACX9K,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAE,cAAA,eAA4B;UAGxBF,EAAA,CAAAmB,UAAA,mBAAA4J,6DAAA;YAAA,OAASL,GAAA,CAAA1B,UAAA,EAAY;UAAA,EAAC;UAEtBhJ,EAAA,CAAAG,MAAA,sBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAETJ,EAAA,CAAA2B,UAAA,KAAAqJ,6CAAA,qBAMS;UAEThL,EAAA,CAAA2B,UAAA,KAAAsJ,6CAAA,qBAaS;UACXjL,EAAA,CAAAI,YAAA,EAAM;UAIRJ,EAAA,CAAA2B,UAAA,KAAAuJ,0CAAA,kBAEM;UAENlL,EAAA,CAAA2B,UAAA,KAAAwJ,0CAAA,kBAEM;UACRnL,EAAA,CAAAI,YAAA,EAAO;;;UAzKkEJ,EAAA,CAAAM,SAAA,IAAyB;UAAzBN,EAAA,CAAAa,kBAAA,KAAA6J,GAAA,CAAAvG,kBAAA,MAAyB;UAK5FnE,EAAA,CAAAM,SAAA,GAAoC;UAApCN,EAAA,CAAAoL,WAAA,UAAAV,GAAA,CAAAvG,kBAAA,MAAoC,qBAAAuG,GAAA,CAAAjB,gBAAA;UAKtCzJ,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAa,kBAAA,MAAA6J,GAAA,CAAAlB,sBAAA,QACF;UAUmCxJ,EAAA,CAAAM,SAAA,GAAc;UAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAqL,eAAA,KAAAC,GAAA,EAAc;UAY7CtL,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAuL,kBAAA,WAAAb,GAAA,CAAA/J,WAAA,UAAA+J,GAAA,CAAApG,UAAA,MACF;UAKEtE,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAO,UAAA,cAAAmK,GAAA,CAAAnG,WAAA,CAAyB;UAEvBvE,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAO,UAAA,SAAAmK,GAAA,CAAA/J,WAAA,OAAuB;UA+EtBX,EAAA,CAAAM,SAAA,GAAqB;UAArBN,EAAA,CAAAO,UAAA,SAAAmK,GAAA,CAAA/J,WAAA,KAAqB;UAiBrBX,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAO,UAAA,SAAAmK,GAAA,CAAA/J,WAAA,GAAA+J,GAAA,CAAApG,UAAA,CAA8B;UAQ9BtE,EAAA,CAAAM,SAAA,GAAgC;UAAhCN,EAAA,CAAAO,UAAA,SAAAmK,GAAA,CAAA/J,WAAA,KAAA+J,GAAA,CAAApG,UAAA,CAAgC;UAiBjCtE,EAAA,CAAAM,SAAA,GAAa;UAAbN,EAAA,CAAAO,UAAA,SAAAmK,GAAA,CAAAjH,OAAA,CAAa;UAIbzD,EAAA,CAAAM,SAAA,GAAW;UAAXN,EAAA,CAAAO,UAAA,SAAAmK,GAAA,CAAA/G,KAAA,CAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}