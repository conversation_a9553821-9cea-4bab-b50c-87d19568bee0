{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:28:14"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 10:28:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:31:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 25ms","timestamp":"2025-05-30 10:31:02"}
{"level":"http","message":"POST / 200 - 71ms","timestamp":"2025-05-30 10:31:02"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:31:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 10:31:02","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 66ms","timestamp":"2025-05-30 10:31:02"}
{"level":"http","message":"POST / 200 - 81ms","timestamp":"2025-05-30 10:31:02"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 57ms","timestamp":"2025-05-30 10:31:02"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 7ms","timestamp":"2025-05-30 10:31:02"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:31:02"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:31:02"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:31:02"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:31:02"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 16ms","timestamp":"2025-05-30 10:31:21"}
{"level":"http","message":"GET /users 200 - 138ms","timestamp":"2025-05-30 10:31:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:31:32","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-30 10:31:32"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 10:31:32"}
{"level":"error","message":"PUT /users/68038b686229308a4e6b3e81/activation 500 - 44ms","timestamp":"2025-05-30 10:31:36"}
{"level":"http","message":"DELETE /users/68038b686229308a4e6b3e81 200 - 24ms","timestamp":"2025-05-30 10:31:39"}
{"level":"http","message":"GET /profile 200 - 23ms","timestamp":"2025-05-30 10:31:57"}
{"level":"http","message":"GET /users 200 - 44ms","timestamp":"2025-05-30 10:31:57"}
{"level":"http","message":"GET /user/68344d3c7230af7cda872c1e 200 - 52ms","timestamp":"2025-05-30 10:31:59"}
{"level":"http","message":"GET /user/68344d3c7230af7cda872c1e 200 - 14ms","timestamp":"2025-05-30 10:32:00"}
{"level":"http","message":"GET / 304 - 14ms","timestamp":"2025-05-30 10:32:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:32:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:32:02"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 10:32:02"}
{"level":"http","message":"GET /users 304 - 54ms","timestamp":"2025-05-30 10:32:06"}
{"level":"http","message":"GET / 304 - 6ms","timestamp":"2025-05-30 10:32:13"}
{"level":"http","message":"GET / 200 - 10ms","timestamp":"2025-05-30 10:32:15"}
{"level":"http","message":"GET / 304 - 16ms","timestamp":"2025-05-30 10:32:15"}
{"level":"http","message":"GET /getev 200 - 25ms","timestamp":"2025-05-30 10:32:17"}
{"level":"http","message":"GET / 200 - 9ms","timestamp":"2025-05-30 10:32:20"}
{"level":"http","message":"GET / 304 - 15ms","timestamp":"2025-05-30 10:32:20"}
{"level":"http","message":"GET / 200 - 19ms","timestamp":"2025-05-30 10:32:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:32:32","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-30 10:32:32"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 10:32:32"}
{"level":"http","message":"GET /users 304 - 59ms","timestamp":"2025-05-30 10:32:33"}
{"level":"http","message":"GET / 304 - 4ms","timestamp":"2025-05-30 10:32:34"}
{"level":"http","message":"GET / 304 - 6ms","timestamp":"2025-05-30 10:32:34"}
{"level":"http","message":"GET / 304 - 9ms","timestamp":"2025-05-30 10:32:34"}
{"level":"http","message":"GET /getev 304 - 7ms","timestamp":"2025-05-30 10:32:34"}
{"level":"http","message":"GET /users 304 - 49ms","timestamp":"2025-05-30 10:32:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:33:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:33:02"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 10:33:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:33:32","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:33:32"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 10:33:32"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:35:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:35:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 10:35:01"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:35:53"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 10:35:53"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:35:54"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:35:54"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:35:54"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:35:54"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:35:54"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:36:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 7ms","timestamp":"2025-05-30 10:36:01"}
{"level":"http","message":"POST / 200 - 29ms","timestamp":"2025-05-30 10:36:01"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:36:28"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 10:36:28"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:36:29"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:36:29"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:36:29"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:36:29"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:36:29"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:36:41"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 10:36:41"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:36:42"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:36:42"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:36:42"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:36:42"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:36:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:37:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 9ms","timestamp":"2025-05-30 10:37:01"}
{"level":"http","message":"POST / 200 - 34ms","timestamp":"2025-05-30 10:37:01"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:37:07"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 10:37:07"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:37:08"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:08"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:09"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:09"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:09"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:37:19"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 10:37:19"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:37:19"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:19"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:19"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:19"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:19"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:37:33"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 10:37:33"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:37:34"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:34"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:34"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:35"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:35"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:37:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:37:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 18ms","timestamp":"2025-05-30 10:37:53"}
{"level":"http","message":"POST / 200 - 48ms","timestamp":"2025-05-30 10:37:53"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:37:53"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:53"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:53"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:53"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:37:53"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 28ms","timestamp":"2025-05-30 10:37:53"}
{"level":"http","message":"GET /users 304 - 49ms","timestamp":"2025-05-30 10:37:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:38:23","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:38:23"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 10:38:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:38:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:38:53"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 10:38:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:39:23","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:39:23"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 10:39:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:39:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:39:53"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 10:39:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:40:23","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:40:23"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 10:40:23"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:40:33"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:40:35","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:40:35"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 10:40:35"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:40:35"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:40:35"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:40:35"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:40:35"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:40:35"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 6ms","timestamp":"2025-05-30 10:40:35"}
{"level":"http","message":"GET /users 304 - 23ms","timestamp":"2025-05-30 10:40:35"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:40:47"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:40:49","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:40:49"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 10:40:49"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:40:49"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:40:49"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:40:49"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:40:49"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:40:49"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 10ms","timestamp":"2025-05-30 10:40:49"}
{"level":"http","message":"GET /users 304 - 36ms","timestamp":"2025-05-30 10:40:49"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:41:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:41:19","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-30 10:41:19"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 10:41:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:41:49","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:41:49"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 10:41:49"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 10:44:42"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 10:44:42"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:44:44"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:44:44"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:44:44"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:44:44"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:44:44"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:44:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:44:47","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 27ms","timestamp":"2025-05-30 10:44:47"}
{"level":"http","message":"POST / 200 - 81ms","timestamp":"2025-05-30 10:44:47"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:44:47"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:44:47"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:44:47"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:44:47"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:44:47"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 125ms","timestamp":"2025-05-30 10:44:47"}
{"level":"http","message":"GET /users 304 - 146ms","timestamp":"2025-05-30 10:44:47"}
{"level":"http","message":"PUT /users/6834468be9e36ff27bd2a6d1/activation 200 - 76ms","timestamp":"2025-05-30 10:44:59"}
{"level":"http","message":"PUT /users/6834468be9e36ff27bd2a6d1/activation 200 - 31ms","timestamp":"2025-05-30 10:45:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:45:17","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 3ms","timestamp":"2025-05-30 10:45:17"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-30 10:45:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:45:47","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:45:47"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 10:45:47"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:46:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:46:16","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:46:16"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 10:46:16"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:46:16"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:16"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:16"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:16"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:16"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 8ms","timestamp":"2025-05-30 10:46:16"}
{"level":"http","message":"GET /users 200 - 28ms","timestamp":"2025-05-30 10:46:16"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:46:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:46:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:46:30"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 10:46:30"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:46:30"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:30"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:30"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:30"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:30"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 14ms","timestamp":"2025-05-30 10:46:30"}
{"level":"http","message":"GET /users 304 - 29ms","timestamp":"2025-05-30 10:46:30"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:46:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:46:47","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:46:47"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 10:46:47"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:46:47"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:47"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:47"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:47"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:46:47"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 8ms","timestamp":"2025-05-30 10:46:47"}
{"level":"http","message":"GET /users 304 - 27ms","timestamp":"2025-05-30 10:46:47"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:47:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:47:09","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:47:09"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 10:47:09"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:47:09"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:47:09"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:47:09"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:47:09"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:47:09"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 18ms","timestamp":"2025-05-30 10:47:09"}
{"level":"http","message":"GET /users 304 - 56ms","timestamp":"2025-05-30 10:47:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:47:39","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-30 10:47:39"}
{"level":"http","message":"POST / 200 - 17ms","timestamp":"2025-05-30 10:47:39"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:48:04"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:48:06","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:48:06"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 10:48:06"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:48:06"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:06"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:06"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:06"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:06"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 11ms","timestamp":"2025-05-30 10:48:06"}
{"level":"http","message":"GET /users 304 - 31ms","timestamp":"2025-05-30 10:48:06"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:48:24"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:48:26","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:48:26"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 10:48:26"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:48:26"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:26"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:26"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:26"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:26"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 10ms","timestamp":"2025-05-30 10:48:26"}
{"level":"http","message":"GET /users 304 - 32ms","timestamp":"2025-05-30 10:48:26"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:48:38"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:48:39","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:48:39"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 10:48:39"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:48:39"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:39"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:39"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:39"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:48:39"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 7ms","timestamp":"2025-05-30 10:48:39"}
{"level":"http","message":"GET /users 304 - 33ms","timestamp":"2025-05-30 10:48:39"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:49:09","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:49:09"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 10:49:09"}
{"level":"error","message":"POST /users 500 - 402ms","timestamp":"2025-05-30 10:49:19"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:49:32"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:49:33","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:49:33"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 10:49:33"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:49:33"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:49:33"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:49:33"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:49:33"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:49:33"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 17ms","timestamp":"2025-05-30 10:49:33"}
{"level":"http","message":"GET /users 304 - 57ms","timestamp":"2025-05-30 10:49:33"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:50:03","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:50:03"}
{"level":"http","message":"POST / 200 - 18ms","timestamp":"2025-05-30 10:50:03"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:50:33","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:50:33"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 10:50:33"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:50:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:50:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:50:46"}
{"level":"http","message":"POST / 200 - 14ms","timestamp":"2025-05-30 10:50:46"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:50:46"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:50:46"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:50:46"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:50:46"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:50:46"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 15ms","timestamp":"2025-05-30 10:50:46"}
{"level":"http","message":"GET /users 304 - 50ms","timestamp":"2025-05-30 10:50:46"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:50:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:51:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:51:00"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 10:51:00"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:51:00"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:51:00"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:51:00"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:51:00"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:51:00"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 9ms","timestamp":"2025-05-30 10:51:00"}
{"level":"http","message":"GET /users 304 - 28ms","timestamp":"2025-05-30 10:51:00"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 10:51:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:51:13","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:51:13"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 10:51:13"}
{"level":"info","message":"WebSocket connection authenticated for user 68344d3c7230af7cda872c1e","timestamp":"2025-05-30 10:51:13"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:51:13"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:51:13"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:51:13"}
{"level":"info","message":"WebSocket operation for user 68344d3c7230af7cda872c1e, operation: subscribe","timestamp":"2025-05-30 10:51:13"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 9ms","timestamp":"2025-05-30 10:51:13"}
{"level":"http","message":"GET /users 304 - 18ms","timestamp":"2025-05-30 10:51:13"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:51:43","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:51:43"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 10:51:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:52:13","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:52:13"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 10:52:13"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:52:43","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:52:43"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 10:52:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:53:13","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:53:13"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 10:53:13"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:53:43","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:53:43"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 10:53:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:55:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:55:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 10:55:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:56:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:56:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 10:56:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:57:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:57:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 10:57:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:58:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 10:58:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 10:58:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 10:59:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 10:59:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 10:59:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:00:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:00:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:00:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:01:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:01:01"}
{"level":"http","message":"POST / 200 - 29ms","timestamp":"2025-05-30 11:01:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:02:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:02:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:02:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:03:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:03:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:03:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:04:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:04:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:04:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:05:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:05:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 11:05:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:06:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:06:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:06:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:07:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:07:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 11:07:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:08:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:08:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:08:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:09:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:09:01"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 11:09:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:10:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:10:01"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-30 11:10:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:11:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:11:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 11:11:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:12:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:12:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:12:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:13:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:13:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:13:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:14:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:14:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:14:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:15:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:15:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:15:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:16:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:16:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:16:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:17:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:17:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:17:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:18:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:18:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:18:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:19:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:19:01"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-30 11:19:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:20:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:20:01"}
{"level":"http","message":"POST / 200 - 14ms","timestamp":"2025-05-30 11:20:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:21:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:21:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:21:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:22:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:22:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:23:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:23:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:23:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:24:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:24:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:24:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:25:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:25:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 11:25:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:26:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:26:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:26:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:27:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:27:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 11:27:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:28:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:28:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:28:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:29:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:29:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:29:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:30:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:30:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:30:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:31:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:31:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 11:31:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:32:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:32:01"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-30 11:32:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:33:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:33:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 11:33:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:34:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:34:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 11:34:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:35:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:35:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:35:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:36:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:36:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:36:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:37:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:37:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 11:37:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:38:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:38:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:38:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:39:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:39:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 11:39:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:40:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:40:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 11:40:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:41:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:41:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 11:41:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:42:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:42:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 11:42:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:43:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:43:01"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-30 11:43:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:44:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:44:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 11:44:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:45:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:45:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 11:45:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:46:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:46:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 11:46:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:47:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:47:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 11:47:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:48:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:48:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 11:48:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:49:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:49:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 11:49:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:50:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:50:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:50:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:51:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:51:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 11:51:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:52:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:52:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 11:52:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:53:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:53:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 11:53:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:54:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:54:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 11:54:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:55:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:55:01"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 11:55:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:56:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:56:01"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-30 11:56:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:57:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 11:57:01"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-30 11:57:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:58:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:58:01"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 11:58:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 11:59:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 11:59:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 11:59:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:00:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:00:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:00:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:01:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:01:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:01:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:02:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:02:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:02:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:03:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:03:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:03:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:04:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:04:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:04:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:05:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:05:01"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 12:05:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:06:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:06:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:06:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:07:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:07:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:07:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:08:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:08:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:08:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:09:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:09:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:09:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:10:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:10:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:10:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:11:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:11:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:11:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:12:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:12:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:12:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:13:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:13:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 12:13:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:14:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:14:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:14:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:15:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:15:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:15:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:16:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:16:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 12:16:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:17:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:17:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 12:17:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:18:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:18:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 12:18:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:19:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:19:01"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 12:19:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:20:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:20:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:20:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:21:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:21:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:21:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:22:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:22:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 12:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:23:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:23:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 12:23:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:24:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:24:01"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 12:24:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:25:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:25:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:25:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:26:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:26:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 12:26:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:27:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:27:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:27:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:28:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:28:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:28:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:29:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:29:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:29:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:30:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:30:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:30:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:31:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:31:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:31:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:32:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:32:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:32:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:33:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:33:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:33:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:34:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:34:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:34:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:35:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:35:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 12:35:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:36:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:36:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 12:36:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:37:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:37:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 12:37:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:38:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:38:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:38:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:39:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:39:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:39:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:40:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:40:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:40:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:41:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:41:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:41:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:42:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:42:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 12:42:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:43:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:43:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:43:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:44:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:44:02"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:44:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:45:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:45:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:45:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:46:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:46:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:46:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:47:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:47:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:47:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:48:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:48:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:48:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:49:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:49:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:49:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:50:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:50:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:50:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:51:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:51:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 12:51:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:52:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:52:01"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-30 12:52:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:53:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:53:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:53:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:54:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:54:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:54:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:55:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:55:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 12:55:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:56:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:56:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:56:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:57:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:57:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:57:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:58:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 12:58:01"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 12:58:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 12:59:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 12:59:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 12:59:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:00:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:00:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:00:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:01:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:01:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:01:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:02:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:02:01"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 13:02:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:03:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:03:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:03:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:04:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:04:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:04:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:05:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:05:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:05:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:06:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:06:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:06:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:07:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:07:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:07:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:08:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:08:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:08:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:09:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:09:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:09:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:10:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:10:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 13:10:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:11:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:11:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:11:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:12:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:12:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:12:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:13:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:13:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 13:13:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:14:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:14:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:14:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:15:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:15:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:15:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:16:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:16:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:16:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:17:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:17:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:17:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:18:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:18:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:18:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:19:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:19:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:19:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:20:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:20:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:20:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:21:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:21:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:21:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:22:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:22:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:22:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:23:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:23:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:23:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:24:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:24:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:24:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:25:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:25:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:25:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:26:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:26:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:26:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:27:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:27:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:27:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:28:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:28:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 13:28:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:29:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:29:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:29:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:30:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:30:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:30:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:31:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:31:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:31:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:32:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:32:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:32:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:33:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:33:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:33:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:34:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:34:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:34:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:35:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:35:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 13:35:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:36:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:36:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 13:36:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:37:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:37:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:37:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:38:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:38:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:38:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:39:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:39:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:39:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:40:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:40:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:40:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:41:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:41:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:41:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:42:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:42:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:42:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:43:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:43:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:43:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:44:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:44:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:44:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:45:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:45:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:45:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:46:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:46:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 13:46:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:47:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:47:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:47:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:48:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:48:01"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 13:48:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:49:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:49:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:49:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:50:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:50:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:50:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:51:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:51:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:51:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:52:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:52:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:52:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:53:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:53:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:53:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:54:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:54:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:54:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:55:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:55:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:55:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:56:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:56:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:56:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:57:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 13:57:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 13:57:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:58:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:58:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:58:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 13:59:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 13:59:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 13:59:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:00:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:00:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:00:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:01:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:01:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:01:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:02:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:02:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:02:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:03:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:03:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:03:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:04:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:04:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:04:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:05:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 14:05:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:05:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:06:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 14:06:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:06:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:07:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:07:01"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-30 14:07:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:08:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:08:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 14:08:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:09:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 14:09:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 14:09:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:10:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:10:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 14:10:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:11:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:11:01"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:11:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:12:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:12:01"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 14:12:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:12:49","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:12:49"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 14:12:49"}
{"level":"http","message":"GET /getev 304 - 8ms","timestamp":"2025-05-30 14:12:50"}
{"level":"http","message":"PUT /logout?secret=2cinfo1&client=esprit 200 - 19ms","timestamp":"2025-05-30 14:12:56"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 14:13:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:13:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:13:00"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-30 14:13:00"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-30 14:13:00"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-30 14:13:04"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-30 14:13:08"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-30 14:13:14"}
{"level":"warn","message":"POST /signup 400 - 7ms","timestamp":"2025-05-30 14:13:14"}
{"level":"warn","message":"Unauthorized connection attempt - No token provided","timestamp":"2025-05-30 14:13:23"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:13:30"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:13:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:13:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 7ms","timestamp":"2025-05-30 14:13:30"}
{"level":"http","message":"POST / 200 - 18ms","timestamp":"2025-05-30 14:13:30"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:15:42"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:15:42"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:26:01"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:26:01"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:26:54"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:26:54"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:27:06"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:27:07"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:27:07"}
{"level":"error","message":"[Database] MongoDB connection error: bad auth : authentication failed","mongoURI":"mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0","stack":"MongoServerError: bad auth : authentication failed\n    at Connection.sendCommand (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connection.js:327:26)\n    at async continueScramConversation (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:131:15)\n    at async executeScram (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:80:5)\n    at async ScramSHA1.auth (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:39:16)\n    at async performInitialHandshake (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connect.js:104:13)\n    at async connect (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connect.js:24:9)","timestamp":"2025-05-30 14:27:07"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:27:18"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:27:18"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:27:18"}
{"level":"error","message":"[Database] MongoDB connection error: bad auth : authentication failed","mongoURI":"mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0","stack":"MongoServerError: bad auth : authentication failed\n    at Connection.sendCommand (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connection.js:327:26)\n    at async continueScramConversation (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:131:15)\n    at async executeScram (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:80:5)\n    at async ScramSHA1.auth (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:39:16)\n    at async performInitialHandshake (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connect.js:104:13)\n    at async connect (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connect.js:24:9)","timestamp":"2025-05-30 14:27:18"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:27:30"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:27:30"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:27:30"}
{"level":"error","message":"[Database] MongoDB connection error: bad auth : authentication failed","mongoURI":"mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0","stack":"MongoServerError: bad auth : authentication failed\n    at Connection.sendCommand (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connection.js:327:26)\n    at async continueScramConversation (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:131:15)\n    at async executeScram (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:80:5)\n    at async ScramSHA1.auth (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:39:16)\n    at async performInitialHandshake (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connect.js:104:13)\n    at async connect (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connect.js:24:9)","timestamp":"2025-05-30 14:27:31"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:27:57"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:27:57"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:27:57"}
{"level":"error","message":"[Database] MongoDB connection error: bad auth : authentication failed","mongoURI":"mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0","stack":"MongoServerError: bad auth : authentication failed\n    at Connection.sendCommand (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connection.js:299:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Connection.command (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connection.js:327:26)\n    at async continueScramConversation (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:131:15)\n    at async executeScram (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:80:5)\n    at async ScramSHA1.auth (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:39:16)\n    at async performInitialHandshake (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connect.js:104:13)\n    at async connect (C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\backend\\node_modules\\mongoose\\node_modules\\mongodb\\lib\\cmap\\connect.js:24:9)","timestamp":"2025-05-30 14:27:57"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:31:36"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:31:37"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:31:37"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-00.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:31:37"}
{"level":"http","message":"POST /signup 201 - 1532ms","timestamp":"2025-05-30 14:32:25"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:34:33"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:34:33"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:34:33"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-02.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:34:34"}
{"level":"http","message":"POST /login 200 - 341ms","timestamp":"2025-05-30 14:39:27"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 60ms","timestamp":"2025-05-30 14:39:32"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:46:12"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:46:12"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:46:12"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-02.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:46:15"}
{"level":"http","message":"POST /login 200 - 283ms","timestamp":"2025-05-30 14:46:21"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 63ms","timestamp":"2025-05-30 14:46:24"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:48:19"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:48:19"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:48:19"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-00.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:48:20"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:48:40"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:48:40"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:48:40"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-00.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:48:41"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:48:55"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:48:55"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:48:55"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-01.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:48:56"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:49:19"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:49:20"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:49:20"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-01.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:49:20"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:49:36"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:49:36"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:49:36"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-00.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:49:37"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:49:48"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:49:48"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:49:48"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-02.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:49:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:50:37","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 30ms","timestamp":"2025-05-30 14:50:37"}
{"level":"http","message":"POST / 200 - 65ms","timestamp":"2025-05-30 14:50:37"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b466666b7ee01e8b5bd2","timestamp":"2025-05-30 14:50:37"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:50:37"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:50:37"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:50:37"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:50:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 14:50:41","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 113ms","timestamp":"2025-05-30 14:50:41"}
{"level":"http","message":"GraphQL anonymous completed in 70ms","timestamp":"2025-05-30 14:50:41"}
{"level":"http","message":"POST / 200 - 87ms","timestamp":"2025-05-30 14:50:41"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 64ms","timestamp":"2025-05-30 14:50:41"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 49ms","timestamp":"2025-05-30 14:50:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:51:07","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 14:51:07"}
{"level":"http","message":"POST / 200 - 34ms","timestamp":"2025-05-30 14:51:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:51:37","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:51:37"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 14:51:37"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 14:51:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:51:59","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 14:51:59"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-30 14:51:59"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b466666b7ee01e8b5bd2","timestamp":"2025-05-30 14:51:59"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:51:59"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:51:59"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:51:59"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:51:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 14:51:59","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 62ms","timestamp":"2025-05-30 14:51:59"}
{"level":"http","message":"GraphQL anonymous completed in 49ms","timestamp":"2025-05-30 14:51:59"}
{"level":"http","message":"POST / 200 - 54ms","timestamp":"2025-05-30 14:51:59"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 54ms","timestamp":"2025-05-30 14:51:59"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 66ms","timestamp":"2025-05-30 14:52:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:52:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:52:29"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 14:52:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:52:59","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 14:52:59"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-30 14:52:59"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 14:53:03"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:53:04","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-30 14:53:04"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 14:53:04"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b466666b7ee01e8b5bd2","timestamp":"2025-05-30 14:53:04"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:53:04"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:53:04"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:53:04"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:53:04"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 14:53:04","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 93ms","timestamp":"2025-05-30 14:53:04"}
{"level":"http","message":"GraphQL anonymous completed in 78ms","timestamp":"2025-05-30 14:53:04"}
{"level":"http","message":"POST / 200 - 82ms","timestamp":"2025-05-30 14:53:04"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 60ms","timestamp":"2025-05-30 14:53:04"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 60ms","timestamp":"2025-05-30 14:53:04"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-30 14:53:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:53:26","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:53:26"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-30 14:53:26"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b466666b7ee01e8b5bd2","timestamp":"2025-05-30 14:53:26"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:53:26"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:53:26"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:53:26"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:53:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-30 14:53:27","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 56ms","timestamp":"2025-05-30 14:53:27"}
{"level":"http","message":"GraphQL anonymous completed in 164ms","timestamp":"2025-05-30 14:53:27"}
{"level":"http","message":"POST / 200 - 168ms","timestamp":"2025-05-30 14:53:27"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 132ms","timestamp":"2025-05-30 14:53:27"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 57ms","timestamp":"2025-05-30 14:53:27"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:53:57","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:53:57"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-30 14:53:57"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-30 14:54:27","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-30 14:54:27"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-30 14:54:27"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:54:41"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:54:41"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:54:41"}
{"level":"info","message":"[Database] MongoDB Connected: ac-srrwniu-shard-00-01.ciy4zys.mongodb.net","timestamp":"2025-05-30 14:54:42"}
{"level":"info","message":"WebSocket connection authenticated for user 6839b466666b7ee01e8b5bd2","timestamp":"2025-05-30 14:54:42"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:54:42"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:54:42"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:54:42"}
{"level":"info","message":"WebSocket operation for user 6839b466666b7ee01e8b5bd2, operation: subscribe","timestamp":"2025-05-30 14:54:42"}
{"level":"info","message":"[Database] Connecting to MongoDB: mongodb+srv://***:***@cluster0.ciy4zys.mongodb.net/project_management?retryWrites=true&w=majority&appName=Cluster0","timestamp":"2025-05-30 14:54:55"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-30 14:54:55"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-30 14:54:55"}
